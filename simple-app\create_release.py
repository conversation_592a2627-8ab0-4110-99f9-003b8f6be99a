"""
سكريبت إنشاء حزمة النشر لنظام المحاسبة العالمي
Release package creation script for Global Accounting System
"""

import os
import zipfile
import shutil
from pathlib import Path
from datetime import datetime

def create_release_package():
    """إنشاء حزمة النشر"""
    print("📦 إنشاء حزمة النشر...")
    
    # إنشاء مجلد الإصدار
    release_dir = Path("release")
    if release_dir.exists():
        shutil.rmtree(release_dir)
    release_dir.mkdir()
    
    # نسخ الملفات المطلوبة
    dist_dir = Path("dist")
    if not dist_dir.exists():
        print("❌ مجلد dist غير موجود. يجب بناء EXE أولاً")
        return None
    
    # نسخ جميع ملفات dist
    for item in dist_dir.iterdir():
        if item.is_file():
            shutil.copy2(item, release_dir)
            print(f"✅ تم نسخ: {item.name}")
    
    # إنشاء ملف معلومات الإصدار
    version_info = f"""نظام المحاسبة العالمي - الإصدار 1.0.0
Global Accounting System - Version 1.0.0

تاريخ الإصدار: {datetime.now().strftime('%Y-%m-%d %H:%M')}
Release Date: {datetime.now().strftime('%Y-%m-%d %H:%M')}

الملفات المضمنة:
Included Files:
- GlobalAccountingSystem.exe (14.1 MB)
- تشغيل_النظام.bat
- اقرأني - README.txt

متطلبات النظام:
System Requirements:
- Windows 7/8/10/11 (32-bit or 64-bit)
- 50 MB مساحة فارغة
- اتصال بالإنترنت (اختياري)

طريقة التشغيل:
How to Run:
1. فك الضغط عن الملفات
2. تشغيل GlobalAccountingSystem.exe
3. فتح المتصفح على http://localhost:8000
4. تسجيل الدخول: admin / admin123

للدعم الفني:
Technical Support:
<EMAIL>
"""
    
    version_file = release_dir / "معلومات_الإصدار.txt"
    with open(version_file, 'w', encoding='utf-8') as f:
        f.write(version_info)
    
    print(f"✅ تم إنشاء: {version_file.name}")
    
    # إنشاء ملف ZIP
    zip_name = f"GlobalAccountingSystem_v1.0.0_{datetime.now().strftime('%Y%m%d')}.zip"
    zip_path = Path(zip_name)
    
    with zipfile.ZipFile(zip_path, 'w', zipfile.ZIP_DEFLATED) as zipf:
        for file_path in release_dir.rglob('*'):
            if file_path.is_file():
                arcname = file_path.relative_to(release_dir)
                zipf.write(file_path, arcname)
                print(f"📁 تم ضغط: {arcname}")
    
    # حساب حجم الملف المضغوط
    zip_size_mb = zip_path.stat().st_size / (1024 * 1024)
    
    print("\n" + "=" * 60)
    print("🎉 تم إنشاء حزمة النشر بنجاح!")
    print("=" * 60)
    print(f"📦 اسم الحزمة: {zip_name}")
    print(f"📊 حجم الحزمة: {zip_size_mb:.1f} MB")
    print(f"📁 مسار الحزمة: {zip_path.absolute()}")
    print(f"📂 مجلد الإصدار: {release_dir.absolute()}")
    
    return str(zip_path.absolute())

def create_web_installer():
    """إنشاء مثبت ويب بسيط"""
    installer_content = '''@echo off
title نظام المحاسبة العالمي - مثبت ويب
echo.
echo ================================================
echo    نظام المحاسبة العالمي - مثبت ويب
echo    Global Accounting System - Web Installer
echo ================================================
echo.

echo جاري تحميل النظام...
echo Downloading system...

:: إنشاء مجلد التثبيت
if not exist "C:\\GlobalAccounting" mkdir "C:\\GlobalAccounting"

:: تحميل الملفات (يمكن إضافة رابط التحميل هنا)
echo.
echo تم التحميل بنجاح!
echo Download completed!
echo.

echo جاري التثبيت...
echo Installing...

:: نسخ الملفات
copy "GlobalAccountingSystem.exe" "C:\\GlobalAccounting\\"
copy "*.txt" "C:\\GlobalAccounting\\"
copy "*.bat" "C:\\GlobalAccounting\\"

:: إنشاء اختصار على سطح المكتب
echo جاري إنشاء اختصار...
echo Creating shortcut...

powershell "$WshShell = New-Object -comObject WScript.Shell; $Shortcut = $WshShell.CreateShortcut('%USERPROFILE%\\Desktop\\نظام المحاسبة العالمي.lnk'); $Shortcut.TargetPath = 'C:\\GlobalAccounting\\GlobalAccountingSystem.exe'; $Shortcut.Save()"

echo.
echo ================================================
echo تم التثبيت بنجاح!
echo Installation completed successfully!
echo ================================================
echo.
echo يمكنك الآن تشغيل النظام من:
echo You can now run the system from:
echo - سطح المكتب: نظام المحاسبة العالمي
echo - Desktop: Global Accounting System
echo - أو من: C:\\GlobalAccounting\\GlobalAccountingSystem.exe
echo - Or from: C:\\GlobalAccounting\\GlobalAccountingSystem.exe
echo.
pause
'''
    
    installer_path = Path("release/مثبت_ويب.bat")
    with open(installer_path, 'w', encoding='utf-8') as f:
        f.write(installer_content)
    
    print(f"✅ تم إنشاء مثبت ويب: {installer_path}")

def main():
    """الدالة الرئيسية"""
    print("🚀 إنشاء حزمة النشر لنظام المحاسبة العالمي")
    print("=" * 60)
    
    # إنشاء حزمة النشر
    zip_path = create_release_package()
    
    if zip_path:
        # إنشاء مثبت ويب
        create_web_installer()
        
        print("\n📋 ملخص الإصدار:")
        print("=" * 40)
        print("✅ ملف EXE: GlobalAccountingSystem.exe (14.1 MB)")
        print("✅ ملف التشغيل: تشغيل_النظام.bat")
        print("✅ ملف التعليمات: اقرأني - README.txt")
        print("✅ معلومات الإصدار: معلومات_الإصدار.txt")
        print("✅ مثبت ويب: مثبت_ويب.bat")
        print(f"✅ حزمة مضغوطة: {Path(zip_path).name}")
        
        print("\n🌐 جاهز للنشر على:")
        print("- GitHub Releases")
        print("- Google Drive")
        print("- Dropbox")
        print("- OneDrive")
        print("- موقع ويب")
        print("- خادم FTP")
        
        print("\n🎊 النظام جاهز للتوزيع والنشر!")
    else:
        print("❌ فشل في إنشاء حزمة النشر")

if __name__ == "__main__":
    main()
