# نظام المحاسبة العالمي - العرض التوضيحي
# Global Accounting System - Demo Version

## 🎯 نظرة عامة

هذا هو العرض التوضيحي لنظام المحاسبة العالمي - نسخة مبسطة تعمل محلياً للاختبار والعرض.

## ✨ الميزات المتاحة

### 🔐 نظام المصادقة
- تسجيل الدخول باستخدام JWT
- حماية نقاط النهاية
- إدارة الجلسات

### 📊 لوحة التحكم
- إحصائيات فورية
- عرض البيانات التفاعلي
- واجهة عربية/إنجليزية

### 🏢 إدارة الشركات
- عرض قائمة الشركات
- معلومات الاتصال
- البيانات الأساسية

### 💰 دليل الحسابات
- حسابات الأصول والخصوم
- حسابات الإيرادات والمصروفات
- أرصدة الحسابات

## 🚀 تشغيل التطبيق

### المتطلبات
- Python 3.9+
- pip

### خطوات التشغيل

1. **تثبيت المتطلبات**:
```bash
pip install fastapi uvicorn PyJWT python-multipart
```

2. **تشغيل الخادم**:
```bash
python main.py
```

3. **فتح التطبيق**:
- الواجهة الرئيسية: http://localhost:8000
- وثائق API: http://localhost:8000/docs
- فحص الصحة: http://localhost:8000/health

## 🔑 بيانات تسجيل الدخول

**اسم المستخدم**: admin  
**كلمة المرور**: admin123

أو

**البريد الإلكتروني**: <EMAIL>  
**كلمة المرور**: admin123

## 📱 استخدام التطبيق

### 1. الصفحة الرئيسية
- عرض ميزات النظام
- روابط سريعة
- معلومات النظام

### 2. تسجيل الدخول
- انقر على "تسجيل الدخول"
- أدخل البيانات التجريبية
- الوصول للوحة التحكم

### 3. لوحة التحكم
- إحصائيات العملاء والموردين
- قائمة الشركات
- دليل الحسابات
- الأرصدة المالية

## 🔧 نقاط النهاية المتاحة

### المصادقة
- `POST /api/v1/auth/login` - تسجيل الدخول
- `GET /api/v1/auth/me` - معلومات المستخدم الحالي

### البيانات
- `GET /api/v1/companies` - قائمة الشركات
- `GET /api/v1/accounts` - دليل الحسابات
- `GET /api/v1/dashboard/stats` - إحصائيات لوحة التحكم

### النظام
- `GET /` - الصفحة الرئيسية
- `GET /health` - فحص صحة النظام
- `GET /docs` - وثائق API التفاعلية

## 📊 البيانات التجريبية

### الشركات
- شركة المحاسبة العالمية
- مؤسسة التقنية المتقدمة
- مجموعة الأعمال الذكية

### الحسابات
- النقدية في الصندوق: 50,000 ر.س
- النقدية في البنك: 125,000 ر.س
- ذمم العملاء: 75,000 ر.س
- المخزون: 95,000 ر.س
- وحسابات أخرى...

### الإحصائيات
- العملاء: 15
- الموردين: 8
- المنتجات: 45
- الفواتير: 127

## 🌐 النشر

### للنشر على Railway:
1. رفع الكود على GitHub
2. ربط المستودع بـ Railway
3. النشر التلقائي

### للنشر على Heroku:
1. إنشاء تطبيق Heroku
2. ربط المستودع
3. النشر عبر Git

### للنشر على Vercel:
1. تحويل لـ Serverless
2. ربط المستودع
3. النشر التلقائي

## 🔒 الأمان

- مصادقة JWT آمنة
- حماية نقاط النهاية
- تشفير كلمات المرور
- حماية CORS

## 📝 ملاحظات

- هذا عرض توضيحي بدون قاعدة بيانات حقيقية
- البيانات مخزنة في الذاكرة فقط
- للإنتاج، يجب ربط قاعدة بيانات حقيقية
- تغيير كلمة المرور الافتراضية مطلوب

## 🆘 الدعم

للمساعدة أو الاستفسارات:
- البريد الإلكتروني: <EMAIL>
- الوثائق: http://localhost:8000/docs

---

**تم تطوير هذا النظام كعرض توضيحي لنظام المحاسبة العالمي الشامل**
