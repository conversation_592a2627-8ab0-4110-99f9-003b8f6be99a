"""
Customer schemas.
"""

from pydantic import BaseModel, EmailStr, validator
from typing import Optional
from decimal import Decimal


class CustomerBase(BaseModel):
    """Base customer schema."""
    name: str
    company_name: Optional[str] = None
    email: Optional[EmailStr] = None
    phone: Optional[str] = None
    mobile: Optional[str] = None
    website: Optional[str] = None
    
    # Billing address
    billing_address_line1: Optional[str] = None
    billing_address_line2: Optional[str] = None
    billing_city: Optional[str] = None
    billing_state: Optional[str] = None
    billing_postal_code: Optional[str] = None
    billing_country: Optional[str] = None
    
    # Shipping address
    shipping_address_line1: Optional[str] = None
    shipping_address_line2: Optional[str] = None
    shipping_city: Optional[str] = None
    shipping_state: Optional[str] = None
    shipping_postal_code: Optional[str] = None
    shipping_country: Optional[str] = None
    
    # Business information
    tax_id: Optional[str] = None
    registration_number: Optional[str] = None
    
    # Financial settings
    credit_limit: Decimal = Decimal('0')
    payment_terms_days: int = 30
    currency: str = "USD"
    
    # Status
    is_active: bool = True
    
    # Notes
    notes: Optional[str] = None
    
    @validator('name')
    def validate_name(cls, v):
        if not v or len(v.strip()) == 0:
            raise ValueError('Customer name is required')
        return v.strip()
    
    @validator('credit_limit')
    def validate_credit_limit(cls, v):
        if v < 0:
            raise ValueError('Credit limit cannot be negative')
        return v
    
    @validator('payment_terms_days')
    def validate_payment_terms(cls, v):
        if v < 0:
            raise ValueError('Payment terms cannot be negative')
        return v


class CustomerCreate(CustomerBase):
    """Customer creation schema."""
    customer_code: Optional[str] = None


class CustomerUpdate(BaseModel):
    """Customer update schema."""
    name: Optional[str] = None
    company_name: Optional[str] = None
    email: Optional[EmailStr] = None
    phone: Optional[str] = None
    mobile: Optional[str] = None
    website: Optional[str] = None
    
    # Billing address
    billing_address_line1: Optional[str] = None
    billing_address_line2: Optional[str] = None
    billing_city: Optional[str] = None
    billing_state: Optional[str] = None
    billing_postal_code: Optional[str] = None
    billing_country: Optional[str] = None
    
    # Shipping address
    shipping_address_line1: Optional[str] = None
    shipping_address_line2: Optional[str] = None
    shipping_city: Optional[str] = None
    shipping_state: Optional[str] = None
    shipping_postal_code: Optional[str] = None
    shipping_country: Optional[str] = None
    
    # Business information
    tax_id: Optional[str] = None
    registration_number: Optional[str] = None
    
    # Financial settings
    credit_limit: Optional[Decimal] = None
    payment_terms_days: Optional[int] = None
    currency: Optional[str] = None
    
    # Status
    is_active: Optional[bool] = None
    
    # Notes
    notes: Optional[str] = None


class CustomerResponse(CustomerBase):
    """Customer response schema."""
    id: int
    customer_code: str
    company_id: int
    display_name: str
    full_billing_address: str
    full_shipping_address: str
    
    class Config:
        from_attributes = True


class CustomerList(BaseModel):
    """Customer list response schema."""
    customers: list[CustomerResponse]
    total: int
    page: int
    size: int
    pages: int


class CustomerSummary(BaseModel):
    """Customer summary schema for reports."""
    id: int
    name: str
    customer_code: str
    total_revenue: Decimal
    outstanding_balance: Decimal
    invoice_count: int
    last_invoice_date: Optional[str] = None
