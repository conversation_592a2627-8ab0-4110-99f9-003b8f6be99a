"""
Database configuration and session management.
"""

from sqlalchemy.ext.asyncio import AsyncSession, create_async_engine, async_sessionmaker
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.pool import Null<PERSON>ool
from typing import AsyncGenerator
import logging

from app.core.config import settings

logger = logging.getLogger(__name__)

# Database URL - use Supabase if available, otherwise fallback to local PostgreSQL
if settings.SUPABASE_URL:
    # Convert Supabase URL to async PostgreSQL URL
    database_url = settings.SUPABASE_URL.replace("postgresql://", "postgresql+asyncpg://")
else:
    # Fallback to local PostgreSQL
    database_url = "postgresql+asyncpg://postgres:password@localhost:5432/accounting_db"

logger.info(f"Using database URL: {database_url.split('@')[0]}@***")

# Create async engine
engine = create_async_engine(
    database_url,
    echo=settings.DEBUG,
    poolclass=NullPool,  # Disable connection pooling for serverless
    pool_pre_ping=True,
    pool_recycle=300,
)

# Create async session factory
AsyncSessionLocal = async_sessionmaker(
    engine,
    class_=AsyncSession,
    expire_on_commit=False,
    autocommit=False,
    autoflush=False,
)

# Create declarative base
Base = declarative_base()


async def get_db() -> AsyncGenerator[AsyncSession, None]:
    """
    Dependency to get database session.
    """
    async with AsyncSessionLocal() as session:
        try:
            yield session
            await session.commit()
        except Exception:
            await session.rollback()
            raise
        finally:
            await session.close()


async def init_db() -> None:
    """
    Initialize database tables.
    """
    async with engine.begin() as conn:
        # Import all models to ensure they are registered
        from app.models import (
            user, company, account, transaction, invoice, 
            customer, supplier, product, inventory
        )
        
        # Create all tables
        await conn.run_sync(Base.metadata.create_all)
        logger.info("Database tables created successfully")


async def close_db() -> None:
    """
    Close database connections.
    """
    await engine.dispose()
    logger.info("Database connections closed")
