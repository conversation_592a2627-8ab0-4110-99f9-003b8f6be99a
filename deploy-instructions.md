# تعليمات النشر - نظام المحاسبة العالمي

## 🚀 خطوات النشر الكاملة

### 1. إعداد قاعدة البيانات (مكتمل ✅)

تم إنشاء قاعدة البيانات على Supabase بنجاح مع:
- **URL**: https://unyoxxghnsllcpaxplvk.supabase.co
- **الجداول المنشأة**: 
  - companies (الشركات)
  - users (المستخدمين)
  - accounts (الحسابات)
  - transactions (المعاملات)
  - transaction_entries (قيود المعاملات)
  - customers (العملاء)
  - suppliers (الموردين)
  - products (المنتجات)
  - invoices (الفواتير)
  - invoice_items (بنود الفواتير)

### 2. بيانات المستخدم الإداري

**البريد الإلكتروني**: <EMAIL>
**اسم المستخدم**: admin
**كلمة المرور**: admin123

### 3. نشر الخادم الخلفي على Railway

```bash
# 1. إنشاء حساب على Railway.app
# 2. ربط مستودع GitHub
# 3. اختيار مجلد backend
# 4. إضافة متغيرات البيئة:

DATABASE_URL=postgresql+asyncpg://postgres.unyoxxghnsllcpaxplvk:GlobalAccounting2024!@aws-0-eu-north-1.pooler.supabase.com:6543/postgres
SECRET_KEY=GlobalAccounting2024SecretKey!@#$%^&*()
SUPABASE_URL=https://unyoxxghnsllcpaxplvk.supabase.co
SUPABASE_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8
```

### 4. نشر الواجهة الأمامية على Vercel

```bash
# 1. إنشاء حساب على Vercel.com
# 2. ربط مستودع GitHub
# 3. اختيار مجلد frontend
# 4. إضافة متغيرات البيئة:

NEXT_PUBLIC_API_URL=https://your-backend-url.railway.app
NEXT_PUBLIC_APP_NAME=نظام المحاسبة العالمي
```

### 5. الروابط المتوقعة بعد النشر

- **الواجهة الأمامية**: https://global-accounting-frontend.vercel.app
- **الخادم الخلفي**: https://global-accounting-backend.railway.app
- **وثائق API**: https://global-accounting-backend.railway.app/docs

### 6. اختبار النشر

```bash
# اختبار الخادم الخلفي
curl https://global-accounting-backend.railway.app/health

# اختبار تسجيل الدخول
curl -X POST https://global-accounting-backend.railway.app/api/v1/auth/login \
  -H "Content-Type: application/x-www-form-urlencoded" \
  -d "username=admin&password=admin123"
```

### 7. الميزات المتاحة

✅ **نظام المصادقة**: تسجيل الدخول والخروج
✅ **إدارة الشركات**: إنشاء وتعديل الشركات
✅ **دليل الحسابات**: حسابات محاسبية كاملة
✅ **إدارة العملاء**: نظام CRM متكامل
✅ **إدارة الموردين**: متابعة الموردين
✅ **إدارة المنتجات**: كتالوج المنتجات
✅ **نظام الفواتير**: إنشاء وإدارة الفواتير
✅ **التقارير المالية**: تقارير شاملة
✅ **لوحة التحكم**: إحصائيات ومؤشرات

### 8. الأمان والحماية

- 🔐 تشفير كلمات المرور باستخدام bcrypt
- 🔑 مصادقة JWT مع انتهاء صلاحية
- 🛡️ حماية CORS مكونة بشكل صحيح
- 🔒 قاعدة بيانات محمية على Supabase
- 📝 سجلات تدقيق شاملة

### 9. الدعم والصيانة

- 📊 مراقبة الأداء عبر Railway/Vercel
- 🔄 نسخ احتياطية تلقائية عبر Supabase
- 📈 تحليلات الاستخدام
- 🐛 تتبع الأخطاء والمشاكل

### 10. التطوير المستقبلي

- 📱 تطبيق موبايل باستخدام React Native
- 🔌 تكاملات مع أنظمة خارجية
- 💳 بوابات دفع إلكتروني
- 📧 إشعارات بريد إلكتروني
- 📊 تقارير متقدمة وذكاء أعمال

---

**ملاحظة**: جميع الملفات والإعدادات جاهزة للنشر. يحتاج المطور فقط لرفع الكود على GitHub وربطه بـ Railway و Vercel.
