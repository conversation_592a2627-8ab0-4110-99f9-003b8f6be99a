"""
Account schemas.
"""

from pydantic import BaseModel, validator
from typing import Optional
from decimal import Decimal
from app.models.account import AccountType


class AccountBase(BaseModel):
    """Base account schema."""
    code: str
    name: str
    description: Optional[str] = None
    account_type: AccountType
    parent_id: Optional[int] = None
    is_active: bool = True
    allow_manual_entries: bool = True
    
    @validator('code')
    def validate_code(cls, v):
        if not v or len(v.strip()) == 0:
            raise ValueError('Account code is required')
        return v.strip()
    
    @validator('name')
    def validate_name(cls, v):
        if not v or len(v.strip()) == 0:
            raise ValueError('Account name is required')
        return v.strip()


class AccountCreate(AccountBase):
    """Account creation schema."""
    company_id: int


class AccountUpdate(BaseModel):
    """Account update schema."""
    code: Optional[str] = None
    name: Optional[str] = None
    description: Optional[str] = None
    parent_id: Optional[int] = None
    is_active: Optional[bool] = None
    allow_manual_entries: Optional[bool] = None


class AccountResponse(AccountBase):
    """Account response schema."""
    id: int
    company_id: int
    level: int
    current_balance: Decimal
    full_code: str
    full_name: str
    is_system: bool
    
    class Config:
        from_attributes = True


class AccountList(BaseModel):
    """Account list response schema."""
    accounts: list[AccountResponse]
    total: int
    page: int
    size: int
    pages: int


class ChartOfAccountsResponse(BaseModel):
    """Chart of accounts response schema."""
    assets: list[AccountResponse]
    liabilities: list[AccountResponse]
    equity: list[AccountResponse]
    revenue: list[AccountResponse]
    expenses: list[AccountResponse]


class TrialBalanceItem(BaseModel):
    """Trial balance item schema."""
    account_id: Optional[int]
    account_code: str
    account_name: str
    account_type: str
    debit_balance: Decimal
    credit_balance: Decimal


class TrialBalanceResponse(BaseModel):
    """Trial balance response schema."""
    items: list[TrialBalanceItem]
    total_debits: Decimal
    total_credits: Decimal
    is_balanced: bool
