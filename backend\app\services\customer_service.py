"""
Customer service for customer management operations.
"""

from typing import Optional, List
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, and_, func, or_

from app.models.customer import Customer
from app.models.invoice import Invoice
from app.schemas.customer import CustomerCreate, CustomerUpdate
from app.services.base_service import BaseService


class CustomerService(BaseService[Customer, CustomerCreate, CustomerUpdate]):
    """Customer service class."""
    
    def __init__(self, db: AsyncSession):
        super().__init__(Customer, db)
    
    async def get_by_code(self, customer_code: str, company_id: int) -> Optional[Customer]:
        """Get customer by code within a company."""
        result = await self.db.execute(
            select(Customer).where(
                and_(
                    Customer.customer_code == customer_code,
                    Customer.company_id == company_id,
                    Customer.is_deleted == False
                )
            )
        )
        return result.scalar_one_or_none()
    
    async def get_by_email(self, email: str, company_id: int) -> Optional[Customer]:
        """Get customer by email within a company."""
        result = await self.db.execute(
            select(Customer).where(
                and_(
                    Customer.email == email,
                    Customer.company_id == company_id,
                    Customer.is_deleted == False
                )
            )
        )
        return result.scalar_one_or_none()
    
    async def search_customers(self, query: str, company_id: int) -> List[Customer]:
        """Search customers by name, email, or company name."""
        result = await self.db.execute(
            select(Customer).where(
                and_(
                    Customer.company_id == company_id,
                    Customer.is_deleted == False,
                    or_(
                        Customer.name.ilike(f"%{query}%"),
                        Customer.company_name.ilike(f"%{query}%"),
                        Customer.email.ilike(f"%{query}%"),
                        Customer.customer_code.ilike(f"%{query}%")
                    )
                )
            ).order_by(Customer.name)
        )
        return result.scalars().all()
    
    async def get_active_customers(self, company_id: int) -> List[Customer]:
        """Get only active customers."""
        result = await self.db.execute(
            select(Customer).where(
                and_(
                    Customer.company_id == company_id,
                    Customer.is_active == True,
                    Customer.is_deleted == False
                )
            ).order_by(Customer.name)
        )
        return result.scalars().all()
    
    async def generate_customer_code(self, company_id: int) -> str:
        """Generate a unique customer code."""
        # Get the highest customer number
        result = await self.db.execute(
            select(func.max(Customer.id)).where(
                and_(
                    Customer.company_id == company_id,
                    Customer.is_deleted == False
                )
            )
        )
        max_id = result.scalar() or 0
        return f"CUST-{max_id + 1:04d}"
    
    async def has_invoices(self, customer_id: int) -> bool:
        """Check if customer has any invoices."""
        result = await self.db.execute(
            select(func.count(Invoice.id)).where(
                and_(
                    Invoice.customer_id == customer_id,
                    Invoice.is_deleted == False
                )
            )
        )
        count = result.scalar()
        return count > 0
    
    async def get_customer_invoices(self, customer_id: int) -> List[Invoice]:
        """Get all invoices for a customer."""
        result = await self.db.execute(
            select(Invoice).where(
                and_(
                    Invoice.customer_id == customer_id,
                    Invoice.is_deleted == False
                )
            ).order_by(Invoice.invoice_date.desc())
        )
        return result.scalars().all()
    
    async def get_customers_with_outstanding_balance(self, company_id: int) -> List[Customer]:
        """Get customers with outstanding balances."""
        # This would require a more complex query joining with invoices
        # For now, return all customers and filter in application logic
        customers = await self.get_active_customers(company_id)
        customers_with_balance = []
        
        for customer in customers:
            balance = customer.get_outstanding_balance()
            if balance > 0:
                customers_with_balance.append(customer)
        
        return customers_with_balance
    
    async def get_top_customers_by_revenue(
        self, 
        company_id: int, 
        limit: int = 10
    ) -> List[dict]:
        """Get top customers by total revenue."""
        query = select(
            Customer.id,
            Customer.name,
            Customer.customer_code,
            func.sum(Invoice.total_amount).label('total_revenue'),
            func.count(Invoice.id).label('invoice_count')
        ).join(Invoice).where(
            and_(
                Customer.company_id == company_id,
                Customer.is_deleted == False,
                Invoice.is_deleted == False
            )
        ).group_by(
            Customer.id, Customer.name, Customer.customer_code
        ).order_by(
            func.sum(Invoice.total_amount).desc()
        ).limit(limit)
        
        result = await self.db.execute(query)
        customers = []
        for row in result:
            customers.append({
                "id": row.id,
                "name": row.name,
                "customer_code": row.customer_code,
                "total_revenue": float(row.total_revenue),
                "invoice_count": row.invoice_count
            })
        
        return customers
