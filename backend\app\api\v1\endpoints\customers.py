"""
Customers endpoints.
"""

from typing import List, Optional
from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.ext.asyncio import AsyncSession

from app.core.database import get_db
from app.core.security import get_current_active_user, require_permissions
from app.models.user import User
from app.services.customer_service import CustomerService
from app.schemas.customer import CustomerResponse, CustomerCreate, CustomerUpdate, CustomerList

router = APIRouter()


@router.get("/", response_model=CustomerList)
async def get_customers(
    skip: int = Query(0, ge=0),
    limit: int = Query(20, ge=1, le=100),
    search: Optional[str] = Query(None),
    active_only: bool = Query(False),
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """Get list of customers with pagination and filtering."""
    customer_service = CustomerService(db)
    
    # Build filters
    filters = {"company_id": current_user.company_id}
    if active_only:
        filters["is_active"] = True
    
    # Get customers
    if search:
        customers = await customer_service.search_customers(search, current_user.company_id)
        total = len(customers)
        customers = customers[skip:skip + limit]
    else:
        customers = await customer_service.get_multi(skip=skip, limit=limit, filters=filters)
        total = await customer_service.count(filters)
    
    return CustomerList(
        customers=customers,
        total=total,
        page=skip // limit + 1,
        size=limit,
        pages=(total + limit - 1) // limit
    )


@router.get("/{customer_id}", response_model=CustomerResponse)
async def get_customer(
    customer_id: int,
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """Get customer by ID."""
    customer_service = CustomerService(db)
    customer = await customer_service.get_by_id(customer_id)
    
    if not customer:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Customer not found"
        )
    
    # Check if customer belongs to user's company
    if customer.company_id != current_user.company_id:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Not enough permissions"
        )
    
    return customer


@router.post("/", response_model=CustomerResponse)
@require_permissions("manage_customers")
async def create_customer(
    customer_data: CustomerCreate,
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """Create a new customer."""
    customer_service = CustomerService(db)
    
    # Set company_id
    customer_dict = customer_data.dict()
    customer_dict["company_id"] = current_user.company_id
    
    # Check if customer code already exists
    if customer_data.customer_code:
        existing_customer = await customer_service.get_by_code(
            customer_data.customer_code, current_user.company_id
        )
        if existing_customer:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Customer code already exists"
            )
    else:
        # Generate customer code
        customer_dict["customer_code"] = await customer_service.generate_customer_code(
            current_user.company_id
        )
    
    # Create customer
    customer = await customer_service.create(customer_dict)
    return customer


@router.put("/{customer_id}", response_model=CustomerResponse)
@require_permissions("manage_customers")
async def update_customer(
    customer_id: int,
    customer_data: CustomerUpdate,
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """Update customer."""
    customer_service = CustomerService(db)
    customer = await customer_service.get_by_id(customer_id)
    
    if not customer:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Customer not found"
        )
    
    # Check if customer belongs to user's company
    if customer.company_id != current_user.company_id:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Not enough permissions"
        )
    
    # Update customer
    updated_customer = await customer_service.update(customer_id, customer_data.dict(exclude_unset=True))
    return updated_customer


@router.delete("/{customer_id}")
@require_permissions("manage_customers")
async def delete_customer(
    customer_id: int,
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """Delete customer."""
    customer_service = CustomerService(db)
    customer = await customer_service.get_by_id(customer_id)
    
    if not customer:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Customer not found"
        )
    
    # Check if customer belongs to user's company
    if customer.company_id != current_user.company_id:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Not enough permissions"
        )
    
    # Check if customer has invoices
    has_invoices = await customer_service.has_invoices(customer_id)
    if has_invoices:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Cannot delete customer with existing invoices"
        )
    
    success = await customer_service.delete(customer_id)
    if not success:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Failed to delete customer"
        )
    
    return {"message": "Customer deleted successfully"}


@router.get("/{customer_id}/invoices")
async def get_customer_invoices(
    customer_id: int,
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """Get customer invoices."""
    customer_service = CustomerService(db)
    customer = await customer_service.get_by_id(customer_id)
    
    if not customer:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Customer not found"
        )
    
    # Check if customer belongs to user's company
    if customer.company_id != current_user.company_id:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Not enough permissions"
        )
    
    invoices = await customer_service.get_customer_invoices(customer_id)
    return {"invoices": invoices}


@router.get("/{customer_id}/balance")
async def get_customer_balance(
    customer_id: int,
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """Get customer outstanding balance."""
    customer_service = CustomerService(db)
    customer = await customer_service.get_by_id(customer_id)
    
    if not customer:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Customer not found"
        )
    
    # Check if customer belongs to user's company
    if customer.company_id != current_user.company_id:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Not enough permissions"
        )
    
    balance = customer.get_outstanding_balance()
    return {
        "customer_id": customer_id,
        "outstanding_balance": float(balance),
        "credit_limit": float(customer.credit_limit),
        "available_credit": float(customer.credit_limit - balance) if customer.credit_limit > 0 else None
    }
