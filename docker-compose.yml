version: '3.8'

services:
  # PostgreSQL Database
  postgres:
    image: postgres:15-alpine
    container_name: accounting_postgres
    environment:
      POSTGRES_DB: accounting_db
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: password
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./database/init.sql:/docker-entrypoint-initdb.d/init.sql
    ports:
      - "5432:5432"
    networks:
      - accounting_network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Redis for caching and background tasks
  redis:
    image: redis:7-alpine
    container_name: accounting_redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - accounting_network
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Backend API
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    container_name: accounting_backend
    environment:
      - DATABASE_URL=postgresql+asyncpg://postgres:password@postgres:5432/accounting_db
      - REDIS_URL=redis://redis:6379
      - SECRET_KEY=your-super-secret-key-change-this-in-production
      - DEBUG=False
      - ALLOWED_HOSTS=["http://localhost:3000", "http://frontend:3000"]
    ports:
      - "8000:8000"
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    networks:
      - accounting_network
    volumes:
      - ./backend/uploads:/app/uploads
    restart: unless-stopped

  # Frontend
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
    container_name: accounting_frontend
    environment:
      - NEXT_PUBLIC_API_URL=http://localhost:8000
      - NODE_ENV=production
    ports:
      - "3000:3000"
    depends_on:
      - backend
    networks:
      - accounting_network
    restart: unless-stopped

  # Nginx Reverse Proxy
  nginx:
    image: nginx:alpine
    container_name: accounting_nginx
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./deployment/nginx/nginx.conf:/etc/nginx/nginx.conf
      - ./deployment/nginx/ssl:/etc/nginx/ssl
    depends_on:
      - frontend
      - backend
    networks:
      - accounting_network
    restart: unless-stopped

volumes:
  postgres_data:
  redis_data:

networks:
  accounting_network:
    driver: bridge
