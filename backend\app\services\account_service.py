"""
Account service for chart of accounts operations.
"""

from typing import Optional, List, Dict, Any
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, and_
from decimal import Decimal

from app.models.account import Account, AccountType
from app.schemas.account import AccountCreate, AccountUpdate
from app.services.base_service import BaseService


class AccountService(BaseService[Account, AccountCreate, AccountUpdate]):
    """Account service class."""
    
    def __init__(self, db: AsyncSession):
        super().__init__(Account, db)
    
    async def get_by_code(self, code: str, company_id: int) -> Optional[Account]:
        """Get account by code within a company."""
        result = await self.db.execute(
            select(Account).where(
                and_(
                    Account.code == code,
                    Account.company_id == company_id,
                    Account.is_deleted == False
                )
            )
        )
        return result.scalar_one_or_none()
    
    async def get_by_company(self, company_id: int) -> List[Account]:
        """Get all accounts for a company."""
        result = await self.db.execute(
            select(Account).where(
                and_(
                    Account.company_id == company_id,
                    Account.is_deleted == False
                )
            ).order_by(Account.code)
        )
        return result.scalars().all()
    
    async def get_by_type(self, account_type: AccountType, company_id: int) -> List[Account]:
        """Get accounts by type for a company."""
        result = await self.db.execute(
            select(Account).where(
                and_(
                    Account.account_type == account_type,
                    Account.company_id == company_id,
                    Account.is_deleted == False
                )
            ).order_by(Account.code)
        )
        return result.scalars().all()
    
    async def get_chart_of_accounts(self, company_id: int) -> Dict[str, List[Account]]:
        """Get organized chart of accounts."""
        accounts = await self.get_by_company(company_id)
        
        chart = {
            "assets": [],
            "liabilities": [],
            "equity": [],
            "revenue": [],
            "expenses": []
        }
        
        for account in accounts:
            if account.account_type == AccountType.ASSET:
                chart["assets"].append(account)
            elif account.account_type == AccountType.LIABILITY:
                chart["liabilities"].append(account)
            elif account.account_type == AccountType.EQUITY:
                chart["equity"].append(account)
            elif account.account_type == AccountType.REVENUE:
                chart["revenue"].append(account)
            elif account.account_type == AccountType.EXPENSE:
                chart["expenses"].append(account)
        
        return chart
    
    async def get_hierarchical_accounts(self, company_id: int) -> List[Dict[str, Any]]:
        """Get accounts in hierarchical structure."""
        accounts = await self.get_by_company(company_id)
        
        # Build hierarchy
        account_dict = {acc.id: acc for acc in accounts}
        root_accounts = []
        
        for account in accounts:
            if account.parent_id is None:
                root_accounts.append(self._build_account_tree(account, account_dict))
        
        return root_accounts
    
    def _build_account_tree(self, account: Account, account_dict: Dict[int, Account]) -> Dict[str, Any]:
        """Build account tree recursively."""
        children = []
        for acc in account_dict.values():
            if acc.parent_id == account.id:
                children.append(self._build_account_tree(acc, account_dict))
        
        return {
            "id": account.id,
            "code": account.code,
            "name": account.name,
            "account_type": account.account_type.value,
            "current_balance": float(account.current_balance),
            "is_active": account.is_active,
            "level": account.level,
            "children": children
        }
    
    async def search_accounts(self, query: str, company_id: int) -> List[Account]:
        """Search accounts by code or name."""
        result = await self.db.execute(
            select(Account).where(
                and_(
                    Account.company_id == company_id,
                    Account.is_deleted == False,
                    (Account.code.ilike(f"%{query}%")) |
                    (Account.name.ilike(f"%{query}%"))
                )
            ).order_by(Account.code)
        )
        return result.scalars().all()
    
    async def get_active_accounts(self, company_id: int) -> List[Account]:
        """Get only active accounts."""
        result = await self.db.execute(
            select(Account).where(
                and_(
                    Account.company_id == company_id,
                    Account.is_active == True,
                    Account.is_deleted == False
                )
            ).order_by(Account.code)
        )
        return result.scalars().all()
    
    async def update_balance(self, account_id: int, amount: Decimal, is_debit: bool) -> Optional[Account]:
        """Update account balance."""
        account = await self.get_by_id(account_id)
        if not account:
            return None
        
        account.update_balance(amount, is_debit)
        await self.db.flush()
        await self.db.refresh(account)
        return account
    
    async def get_trial_balance(self, company_id: int) -> List[Dict[str, Any]]:
        """Get trial balance for all accounts."""
        accounts = await self.get_by_company(company_id)
        
        trial_balance = []
        total_debits = Decimal('0')
        total_credits = Decimal('0')
        
        for account in accounts:
            if account.current_balance != 0:
                if account.is_debit_account():
                    debit_balance = account.current_balance if account.current_balance > 0 else Decimal('0')
                    credit_balance = abs(account.current_balance) if account.current_balance < 0 else Decimal('0')
                else:
                    credit_balance = account.current_balance if account.current_balance > 0 else Decimal('0')
                    debit_balance = abs(account.current_balance) if account.current_balance < 0 else Decimal('0')
                
                trial_balance.append({
                    "account_id": account.id,
                    "account_code": account.code,
                    "account_name": account.name,
                    "account_type": account.account_type.value,
                    "debit_balance": float(debit_balance),
                    "credit_balance": float(credit_balance)
                })
                
                total_debits += debit_balance
                total_credits += credit_balance
        
        trial_balance.append({
            "account_id": None,
            "account_code": "TOTAL",
            "account_name": "Total",
            "account_type": "total",
            "debit_balance": float(total_debits),
            "credit_balance": float(total_credits)
        })
        
        return trial_balance
