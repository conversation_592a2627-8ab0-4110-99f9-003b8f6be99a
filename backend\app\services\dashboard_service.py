"""
Dashboard service for dashboard data and analytics.
"""

from typing import List, Dict, Any, Optional
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, func, and_, desc
from datetime import date, datetime, timedelta
from decimal import Decimal

from app.models.invoice import Invoice, InvoiceStatus, InvoiceType
from app.models.customer import Customer
from app.models.product import Product
from app.models.account import Account, AccountType
from app.models.transaction import Transaction, TransactionEntry


class DashboardService:
    """Dashboard service class."""
    
    def __init__(self, db: AsyncSession):
        self.db = db
    
    async def get_dashboard_stats(self, company_id: int) -> Dict[str, Any]:
        """Get main dashboard statistics."""
        # Get current month and year
        today = date.today()
        current_month_start = today.replace(day=1)
        last_month_start = (current_month_start - timedelta(days=1)).replace(day=1)
        last_month_end = current_month_start - timedelta(days=1)
        
        # Total Revenue (current month)
        revenue_query = select(func.sum(Invoice.total_amount)).where(
            and_(
                Invoice.company_id == company_id,
                Invoice.invoice_type == InvoiceType.SALES,
                Invoice.status.in_([InvoiceStatus.SENT, InvoiceStatus.PAID]),
                Invoice.invoice_date >= current_month_start,
                Invoice.is_deleted == False
            )
        )
        current_revenue = await self.db.execute(revenue_query)
        current_revenue = current_revenue.scalar() or Decimal('0')
        
        # Last month revenue for comparison
        last_revenue_query = select(func.sum(Invoice.total_amount)).where(
            and_(
                Invoice.company_id == company_id,
                Invoice.invoice_type == InvoiceType.SALES,
                Invoice.status.in_([InvoiceStatus.SENT, InvoiceStatus.PAID]),
                Invoice.invoice_date >= last_month_start,
                Invoice.invoice_date <= last_month_end,
                Invoice.is_deleted == False
            )
        )
        last_revenue = await self.db.execute(last_revenue_query)
        last_revenue = last_revenue.scalar() or Decimal('0')
        
        # Calculate revenue change
        revenue_change = 0
        if last_revenue > 0:
            revenue_change = float((current_revenue - last_revenue) / last_revenue * 100)
        
        # Total Expenses (current month)
        expense_query = select(func.sum(Invoice.total_amount)).where(
            and_(
                Invoice.company_id == company_id,
                Invoice.invoice_type == InvoiceType.PURCHASE,
                Invoice.status.in_([InvoiceStatus.RECEIVED, InvoiceStatus.PAID]),
                Invoice.invoice_date >= current_month_start,
                Invoice.is_deleted == False
            )
        )
        current_expenses = await self.db.execute(expense_query)
        current_expenses = current_expenses.scalar() or Decimal('0')
        
        # Outstanding Invoices
        outstanding_query = select(func.sum(Invoice.total_amount - Invoice.paid_amount)).where(
            and_(
                Invoice.company_id == company_id,
                Invoice.invoice_type == InvoiceType.SALES,
                Invoice.status.in_([InvoiceStatus.SENT, InvoiceStatus.OVERDUE]),
                Invoice.is_deleted == False
            )
        )
        outstanding = await self.db.execute(outstanding_query)
        outstanding = outstanding.scalar() or Decimal('0')
        
        # Net Profit
        net_profit = current_revenue - current_expenses
        
        return {
            "total_revenue": float(current_revenue),
            "total_expenses": float(current_expenses),
            "net_profit": float(net_profit),
            "outstanding_invoices": float(outstanding),
            "revenue_change": revenue_change,
            "period": "current_month"
        }
    
    async def get_recent_activity(self, company_id: int, limit: int = 10) -> List[Dict[str, Any]]:
        """Get recent activity."""
        activities = []
        
        # Recent invoices
        invoice_query = select(Invoice).where(
            and_(
                Invoice.company_id == company_id,
                Invoice.is_deleted == False
            )
        ).order_by(desc(Invoice.created_at)).limit(limit // 2)
        
        invoices = await self.db.execute(invoice_query)
        for invoice in invoices.scalars():
            activities.append({
                "type": "invoice",
                "title": f"Invoice {invoice.invoice_number} created",
                "description": f"Invoice for ${invoice.total_amount} created",
                "timestamp": invoice.created_at,
                "reference_id": invoice.id
            })
        
        # Recent transactions
        transaction_query = select(Transaction).where(
            and_(
                Transaction.company_id == company_id,
                Transaction.is_deleted == False
            )
        ).order_by(desc(Transaction.created_at)).limit(limit // 2)
        
        transactions = await self.db.execute(transaction_query)
        for transaction in transactions.scalars():
            activities.append({
                "type": "transaction",
                "title": f"Transaction {transaction.reference} posted",
                "description": transaction.description,
                "timestamp": transaction.created_at,
                "reference_id": transaction.id
            })
        
        # Sort by timestamp and limit
        activities.sort(key=lambda x: x["timestamp"], reverse=True)
        return activities[:limit]
    
    async def get_revenue_chart_data(
        self, 
        company_id: int, 
        start_date: date, 
        end_date: date
    ) -> List[Dict[str, Any]]:
        """Get revenue chart data."""
        query = select(
            func.date(Invoice.invoice_date).label('date'),
            func.sum(Invoice.total_amount).label('amount')
        ).where(
            and_(
                Invoice.company_id == company_id,
                Invoice.invoice_type == InvoiceType.SALES,
                Invoice.status.in_([InvoiceStatus.SENT, InvoiceStatus.PAID]),
                Invoice.invoice_date >= start_date,
                Invoice.invoice_date <= end_date,
                Invoice.is_deleted == False
            )
        ).group_by(func.date(Invoice.invoice_date)).order_by('date')
        
        result = await self.db.execute(query)
        data = []
        for row in result:
            data.append({
                "date": row.date.isoformat(),
                "amount": float(row.amount or 0)
            })
        
        return data
    
    async def get_expenses_chart_data(
        self, 
        company_id: int, 
        start_date: date, 
        end_date: date
    ) -> List[Dict[str, Any]]:
        """Get expenses chart data."""
        query = select(
            func.date(Invoice.invoice_date).label('date'),
            func.sum(Invoice.total_amount).label('amount')
        ).where(
            and_(
                Invoice.company_id == company_id,
                Invoice.invoice_type == InvoiceType.PURCHASE,
                Invoice.status.in_([InvoiceStatus.RECEIVED, InvoiceStatus.PAID]),
                Invoice.invoice_date >= start_date,
                Invoice.invoice_date <= end_date,
                Invoice.is_deleted == False
            )
        ).group_by(func.date(Invoice.invoice_date)).order_by('date')
        
        result = await self.db.execute(query)
        data = []
        for row in result:
            data.append({
                "date": row.date.isoformat(),
                "amount": float(row.amount or 0)
            })
        
        return data
    
    async def get_profit_chart_data(
        self, 
        company_id: int, 
        start_date: date, 
        end_date: date
    ) -> List[Dict[str, Any]]:
        """Get profit chart data."""
        # This would require more complex calculation
        # For now, return mock data
        return [
            {"date": start_date.isoformat(), "amount": 1000},
            {"date": end_date.isoformat(), "amount": 1500}
        ]
    
    async def get_cash_flow_chart_data(
        self, 
        company_id: int, 
        start_date: date, 
        end_date: date
    ) -> List[Dict[str, Any]]:
        """Get cash flow chart data."""
        # This would require complex calculation based on cash accounts
        # For now, return mock data
        return [
            {"date": start_date.isoformat(), "inflow": 5000, "outflow": 3000},
            {"date": end_date.isoformat(), "inflow": 6000, "outflow": 3500}
        ]
    
    async def get_kpis(self, company_id: int) -> Dict[str, Any]:
        """Get key performance indicators."""
        # This would calculate various KPIs
        return {
            "gross_margin": 35.5,
            "net_margin": 12.3,
            "current_ratio": 2.1,
            "debt_to_equity": 0.4,
            "inventory_turnover": 6.2,
            "accounts_receivable_turnover": 8.5
        }
    
    async def get_alerts(self, company_id: int) -> List[Dict[str, Any]]:
        """Get system alerts."""
        alerts = []
        
        # Check for overdue invoices
        overdue_count = await self.db.execute(
            select(func.count(Invoice.id)).where(
                and_(
                    Invoice.company_id == company_id,
                    Invoice.status == InvoiceStatus.OVERDUE,
                    Invoice.is_deleted == False
                )
            )
        )
        overdue_count = overdue_count.scalar()
        
        if overdue_count > 0:
            alerts.append({
                "type": "warning",
                "title": "Overdue Invoices",
                "message": f"You have {overdue_count} overdue invoices",
                "action_url": "/dashboard/invoices?status=overdue"
            })
        
        # Check for low stock items (would need inventory implementation)
        # For now, add a mock alert
        alerts.append({
            "type": "info",
            "title": "Low Stock Alert",
            "message": "3 products are running low on stock",
            "action_url": "/dashboard/inventory"
        })
        
        return alerts
    
    async def get_top_customers(
        self, 
        company_id: int, 
        start_date: date, 
        end_date: date, 
        limit: int = 5
    ) -> List[Dict[str, Any]]:
        """Get top customers by revenue."""
        query = select(
            Customer.id,
            Customer.name,
            func.sum(Invoice.total_amount).label('total_revenue')
        ).join(Invoice).where(
            and_(
                Invoice.company_id == company_id,
                Invoice.invoice_type == InvoiceType.SALES,
                Invoice.invoice_date >= start_date,
                Invoice.invoice_date <= end_date,
                Invoice.is_deleted == False
            )
        ).group_by(Customer.id, Customer.name).order_by(desc('total_revenue')).limit(limit)
        
        result = await self.db.execute(query)
        customers = []
        for row in result:
            customers.append({
                "id": row.id,
                "name": row.name,
                "total_revenue": float(row.total_revenue)
            })
        
        return customers
    
    async def get_top_products(
        self, 
        company_id: int, 
        start_date: date, 
        end_date: date, 
        limit: int = 5
    ) -> List[Dict[str, Any]]:
        """Get top products by sales."""
        # This would require invoice items implementation
        # For now, return mock data
        return [
            {"id": 1, "name": "Product A", "quantity_sold": 150, "revenue": 15000},
            {"id": 2, "name": "Product B", "quantity_sold": 120, "revenue": 12000},
            {"id": 3, "name": "Product C", "quantity_sold": 100, "revenue": 10000},
        ]
    
    async def get_cash_flow_summary(self, company_id: int) -> Dict[str, Any]:
        """Get cash flow summary."""
        # This would calculate actual cash flow from cash accounts
        # For now, return mock data
        return {
            "opening_balance": 25000,
            "total_inflows": 15000,
            "total_outflows": 8000,
            "closing_balance": 32000,
            "net_cash_flow": 7000
        }
