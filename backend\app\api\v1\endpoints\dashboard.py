"""
Dashboard endpoints.
"""

from typing import List, Optional
from fastapi import APIRouter, Depends, Query
from sqlalchemy.ext.asyncio import AsyncSession
from datetime import date, datetime, timedelta

from app.core.database import get_db
from app.core.security import get_current_active_user
from app.models.user import User
from app.services.dashboard_service import DashboardService

router = APIRouter()


@router.get("/stats")
async def get_dashboard_stats(
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """Get dashboard statistics."""
    dashboard_service = DashboardService(db)
    stats = await dashboard_service.get_dashboard_stats(current_user.company_id)
    return stats


@router.get("/activity")
async def get_recent_activity(
    limit: int = Query(10, ge=1, le=50),
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """Get recent activity."""
    dashboard_service = DashboardService(db)
    activity = await dashboard_service.get_recent_activity(current_user.company_id, limit)
    return {"activity": activity}


@router.get("/charts/{chart_type}")
async def get_chart_data(
    chart_type: str,
    period: str = Query("30d", regex="^(7d|30d|90d|1y)$"),
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """Get chart data for dashboard."""
    dashboard_service = DashboardService(db)
    
    # Calculate date range based on period
    end_date = date.today()
    if period == "7d":
        start_date = end_date - timedelta(days=7)
    elif period == "30d":
        start_date = end_date - timedelta(days=30)
    elif period == "90d":
        start_date = end_date - timedelta(days=90)
    elif period == "1y":
        start_date = end_date - timedelta(days=365)
    else:
        start_date = end_date - timedelta(days=30)
    
    if chart_type == "revenue":
        data = await dashboard_service.get_revenue_chart_data(
            current_user.company_id, start_date, end_date
        )
    elif chart_type == "expenses":
        data = await dashboard_service.get_expenses_chart_data(
            current_user.company_id, start_date, end_date
        )
    elif chart_type == "profit":
        data = await dashboard_service.get_profit_chart_data(
            current_user.company_id, start_date, end_date
        )
    elif chart_type == "cash-flow":
        data = await dashboard_service.get_cash_flow_chart_data(
            current_user.company_id, start_date, end_date
        )
    else:
        return {"error": "Invalid chart type"}
    
    return {"data": data, "period": period}


@router.get("/kpis")
async def get_key_performance_indicators(
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """Get key performance indicators."""
    dashboard_service = DashboardService(db)
    kpis = await dashboard_service.get_kpis(current_user.company_id)
    return kpis


@router.get("/alerts")
async def get_alerts(
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """Get system alerts and notifications."""
    dashboard_service = DashboardService(db)
    alerts = await dashboard_service.get_alerts(current_user.company_id)
    return {"alerts": alerts}


@router.get("/top-customers")
async def get_top_customers(
    limit: int = Query(5, ge=1, le=20),
    period: str = Query("30d", regex="^(7d|30d|90d|1y)$"),
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """Get top customers by revenue."""
    dashboard_service = DashboardService(db)
    
    # Calculate date range
    end_date = date.today()
    if period == "7d":
        start_date = end_date - timedelta(days=7)
    elif period == "30d":
        start_date = end_date - timedelta(days=30)
    elif period == "90d":
        start_date = end_date - timedelta(days=90)
    elif period == "1y":
        start_date = end_date - timedelta(days=365)
    else:
        start_date = end_date - timedelta(days=30)
    
    customers = await dashboard_service.get_top_customers(
        current_user.company_id, start_date, end_date, limit
    )
    return {"customers": customers, "period": period}


@router.get("/top-products")
async def get_top_products(
    limit: int = Query(5, ge=1, le=20),
    period: str = Query("30d", regex="^(7d|30d|90d|1y)$"),
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """Get top products by sales."""
    dashboard_service = DashboardService(db)
    
    # Calculate date range
    end_date = date.today()
    if period == "7d":
        start_date = end_date - timedelta(days=7)
    elif period == "30d":
        start_date = end_date - timedelta(days=30)
    elif period == "90d":
        start_date = end_date - timedelta(days=90)
    elif period == "1y":
        start_date = end_date - timedelta(days=365)
    else:
        start_date = end_date - timedelta(days=30)
    
    products = await dashboard_service.get_top_products(
        current_user.company_id, start_date, end_date, limit
    )
    return {"products": products, "period": period}


@router.get("/cash-flow-summary")
async def get_cash_flow_summary(
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """Get cash flow summary."""
    dashboard_service = DashboardService(db)
    summary = await dashboard_service.get_cash_flow_summary(current_user.company_id)
    return summary
