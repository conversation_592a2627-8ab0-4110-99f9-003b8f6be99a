#!/bin/bash

# سكريبت اختبار النشر - نظام المحاسبة العالمي
# Test Deployment Script - Global Accounting System

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# URLs
BACKEND_URL="https://global-accounting-backend.railway.app"
FRONTEND_URL="https://global-accounting-frontend.vercel.app"

print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Test Backend Health
test_backend_health() {
    print_status "اختبار صحة الخادم الخلفي..."
    
    if curl -f -s "$BACKEND_URL/health" > /dev/null; then
        print_success "الخادم الخلفي يعمل بشكل صحيح"
        
        # Get health details
        HEALTH_RESPONSE=$(curl -s "$BACKEND_URL/health")
        echo "استجابة الصحة: $HEALTH_RESPONSE"
    else
        print_error "فشل في الوصول للخادم الخلفي"
        return 1
    fi
}

# Test API Documentation
test_api_docs() {
    print_status "اختبار وثائق API..."
    
    if curl -f -s "$BACKEND_URL/docs" > /dev/null; then
        print_success "وثائق API متاحة"
        echo "رابط الوثائق: $BACKEND_URL/docs"
    else
        print_error "فشل في الوصول لوثائق API"
        return 1
    fi
}

# Test Authentication
test_authentication() {
    print_status "اختبار نظام المصادقة..."
    
    # Test login with admin credentials
    LOGIN_RESPONSE=$(curl -s -X POST "$BACKEND_URL/api/v1/auth/login" \
        -H "Content-Type: application/x-www-form-urlencoded" \
        -d "username=admin&password=admin123")
    
    if echo "$LOGIN_RESPONSE" | grep -q "access_token"; then
        print_success "تسجيل الدخول يعمل بشكل صحيح"
        
        # Extract token for further tests
        ACCESS_TOKEN=$(echo "$LOGIN_RESPONSE" | grep -o '"access_token":"[^"]*"' | cut -d'"' -f4)
        echo "تم الحصول على رمز الوصول بنجاح"
    else
        print_error "فشل في تسجيل الدخول"
        echo "استجابة تسجيل الدخول: $LOGIN_RESPONSE"
        return 1
    fi
}

# Test Database Connection
test_database() {
    print_status "اختبار الاتصال بقاعدة البيانات..."
    
    # Test getting companies (requires authentication)
    if [ -n "$ACCESS_TOKEN" ]; then
        COMPANIES_RESPONSE=$(curl -s -H "Authorization: Bearer $ACCESS_TOKEN" \
            "$BACKEND_URL/api/v1/companies/")
        
        if echo "$COMPANIES_RESPONSE" | grep -q "companies"; then
            print_success "قاعدة البيانات متصلة وتعمل بشكل صحيح"
        else
            print_error "مشكلة في الاتصال بقاعدة البيانات"
            echo "استجابة الشركات: $COMPANIES_RESPONSE"
            return 1
        fi
    else
        print_warning "تخطي اختبار قاعدة البيانات - لا يوجد رمز وصول"
    fi
}

# Test Frontend
test_frontend() {
    print_status "اختبار الواجهة الأمامية..."
    
    if curl -f -s "$FRONTEND_URL" > /dev/null; then
        print_success "الواجهة الأمامية تعمل بشكل صحيح"
        echo "رابط الموقع: $FRONTEND_URL"
    else
        print_error "فشل في الوصول للواجهة الأمامية"
        return 1
    fi
}

# Test API Endpoints
test_api_endpoints() {
    print_status "اختبار نقاط النهاية الأساسية..."
    
    if [ -n "$ACCESS_TOKEN" ]; then
        # Test accounts endpoint
        ACCOUNTS_RESPONSE=$(curl -s -H "Authorization: Bearer $ACCESS_TOKEN" \
            "$BACKEND_URL/api/v1/accounts/")
        
        if echo "$ACCOUNTS_RESPONSE" | grep -q "accounts"; then
            print_success "نقطة نهاية الحسابات تعمل"
        else
            print_warning "مشكلة في نقطة نهاية الحسابات"
        fi
        
        # Test customers endpoint
        CUSTOMERS_RESPONSE=$(curl -s -H "Authorization: Bearer $ACCESS_TOKEN" \
            "$BACKEND_URL/api/v1/customers/")
        
        if echo "$CUSTOMERS_RESPONSE" | grep -q "customers"; then
            print_success "نقطة نهاية العملاء تعمل"
        else
            print_warning "مشكلة في نقطة نهاية العملاء"
        fi
    else
        print_warning "تخطي اختبار نقاط النهاية - لا يوجد رمز وصول"
    fi
}

# Main test function
main() {
    echo "======================================"
    echo "اختبار نشر نظام المحاسبة العالمي"
    echo "Global Accounting System Deployment Test"
    echo "======================================"
    echo ""
    
    # Run all tests
    test_backend_health
    test_api_docs
    test_authentication
    test_database
    test_frontend
    test_api_endpoints
    
    echo ""
    print_success "🎉 اكتمل اختبار النشر بنجاح!"
    echo ""
    echo "الروابط المتاحة:"
    echo "📱 الواجهة الأمامية: $FRONTEND_URL"
    echo "🔧 الخادم الخلفي: $BACKEND_URL"
    echo "📚 وثائق API: $BACKEND_URL/docs"
    echo ""
    echo "بيانات تسجيل الدخول:"
    echo "📧 البريد الإلكتروني: <EMAIL>"
    echo "🔑 كلمة المرور: admin123"
    echo ""
    echo "ملاحظة: تأكد من تغيير كلمة المرور الافتراضية في الإنتاج!"
}

# Run main function
main
