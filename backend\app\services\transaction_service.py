"""
Transaction service for transaction operations.
"""

from typing import Optional, List, Dict, Any
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, and_, func, or_
from sqlalchemy.orm import selectinload
from datetime import date
from decimal import Decimal

from app.models.transaction import Transaction, TransactionEntry
from app.models.account import Account
from app.schemas.transaction import TransactionCreate, TransactionUpdate
from app.services.base_service import BaseService


class TransactionService(BaseService[Transaction, TransactionCreate, TransactionUpdate]):
    """Transaction service class."""
    
    def __init__(self, db: AsyncSession):
        super().__init__(Transaction, db)
    
    async def get_by_reference(self, reference: str, company_id: int) -> Optional[Transaction]:
        """Get transaction by reference within a company."""
        result = await self.db.execute(
            select(Transaction).options(
                selectinload(Transaction.entries).selectinload(TransactionEntry.account)
            ).where(
                and_(
                    Transaction.reference == reference,
                    Transaction.company_id == company_id,
                    Transaction.is_deleted == False
                )
            )
        )
        return result.scalar_one_or_none()
    
    async def get_transactions_filtered(
        self,
        company_id: int,
        skip: int = 0,
        limit: int = 20,
        search: Optional[str] = None,
        start_date: Optional[date] = None,
        end_date: Optional[date] = None,
        posted_only: bool = False
    ) -> List[Transaction]:
        """Get transactions with filtering."""
        query = select(Transaction).options(
            selectinload(Transaction.entries).selectinload(TransactionEntry.account)
        ).where(
            and_(
                Transaction.company_id == company_id,
                Transaction.is_deleted == False
            )
        )
        
        # Apply filters
        if search:
            query = query.where(
                or_(
                    Transaction.reference.ilike(f"%{search}%"),
                    Transaction.description.ilike(f"%{search}%")
                )
            )
        
        if start_date:
            query = query.where(Transaction.transaction_date >= start_date)
        
        if end_date:
            query = query.where(Transaction.transaction_date <= end_date)
        
        if posted_only:
            query = query.where(Transaction.is_posted == True)
        
        # Apply ordering and pagination
        query = query.order_by(Transaction.transaction_date.desc(), Transaction.id.desc())
        query = query.offset(skip).limit(limit)
        
        result = await self.db.execute(query)
        return result.scalars().all()
    
    async def count_transactions_filtered(
        self,
        company_id: int,
        search: Optional[str] = None,
        start_date: Optional[date] = None,
        end_date: Optional[date] = None,
        posted_only: bool = False
    ) -> int:
        """Count transactions with filtering."""
        query = select(func.count(Transaction.id)).where(
            and_(
                Transaction.company_id == company_id,
                Transaction.is_deleted == False
            )
        )
        
        # Apply filters
        if search:
            query = query.where(
                or_(
                    Transaction.reference.ilike(f"%{search}%"),
                    Transaction.description.ilike(f"%{search}%")
                )
            )
        
        if start_date:
            query = query.where(Transaction.transaction_date >= start_date)
        
        if end_date:
            query = query.where(Transaction.transaction_date <= end_date)
        
        if posted_only:
            query = query.where(Transaction.is_posted == True)
        
        result = await self.db.execute(query)
        return result.scalar()
    
    async def create_transaction(self, transaction_data: Dict[str, Any]) -> Transaction:
        """Create a transaction with entries."""
        # Extract entries data
        entries_data = transaction_data.pop("entries", [])
        
        # Create transaction
        transaction = Transaction(**transaction_data)
        self.db.add(transaction)
        await self.db.flush()
        
        # Create entries
        for entry_data in entries_data:
            entry = TransactionEntry(
                transaction_id=transaction.id,
                **entry_data
            )
            self.db.add(entry)
        
        await self.db.flush()
        await self.db.refresh(transaction)
        
        # Load entries
        result = await self.db.execute(
            select(Transaction).options(
                selectinload(Transaction.entries).selectinload(TransactionEntry.account)
            ).where(Transaction.id == transaction.id)
        )
        return result.scalar_one()
    
    async def update_transaction(self, transaction_id: int, update_data: Dict[str, Any]) -> Optional[Transaction]:
        """Update a transaction and its entries."""
        transaction = await self.get_by_id(transaction_id)
        if not transaction:
            return None
        
        # Extract entries data if provided
        entries_data = update_data.pop("entries", None)
        
        # Update transaction fields
        for key, value in update_data.items():
            if hasattr(transaction, key):
                setattr(transaction, key, value)
        
        # Update entries if provided
        if entries_data is not None:
            # Delete existing entries
            for entry in transaction.entries:
                await self.db.delete(entry)
            
            # Create new entries
            for entry_data in entries_data:
                entry = TransactionEntry(
                    transaction_id=transaction.id,
                    **entry_data
                )
                self.db.add(entry)
        
        await self.db.flush()
        await self.db.refresh(transaction)
        return transaction
    
    async def post_transaction(self, transaction_id: int) -> bool:
        """Post a transaction to update account balances."""
        result = await self.db.execute(
            select(Transaction).options(
                selectinload(Transaction.entries).selectinload(TransactionEntry.account)
            ).where(Transaction.id == transaction_id)
        )
        transaction = result.scalar_one_or_none()
        
        if not transaction or transaction.is_posted:
            return False
        
        # Check if transaction is balanced
        if not transaction.is_balanced:
            return False
        
        # Update account balances
        for entry in transaction.entries:
            if entry.debit_amount > 0:
                entry.account.update_balance(entry.debit_amount, True)
            if entry.credit_amount > 0:
                entry.account.update_balance(entry.credit_amount, False)
        
        # Mark transaction as posted
        transaction.is_posted = True
        
        await self.db.flush()
        return True
    
    async def reverse_transaction(
        self,
        transaction_id: int,
        reversal_date: date,
        description: Optional[str] = None,
        created_by_id: int = None
    ) -> Transaction:
        """Create a reversal transaction."""
        result = await self.db.execute(
            select(Transaction).options(
                selectinload(Transaction.entries).selectinload(TransactionEntry.account)
            ).where(Transaction.id == transaction_id)
        )
        original_transaction = result.scalar_one()
        
        # Create reversal transaction
        reversal = Transaction(
            reference=f"REV-{original_transaction.reference}",
            description=description or f"Reversal of {original_transaction.description}",
            transaction_date=reversal_date,
            company_id=original_transaction.company_id,
            created_by_id=created_by_id,
            source_type="reversal",
            source_id=original_transaction.id
        )
        
        self.db.add(reversal)
        await self.db.flush()
        
        # Create reversal entries (swap debits and credits)
        for entry in original_transaction.entries:
            reversal_entry = TransactionEntry(
                transaction_id=reversal.id,
                account_id=entry.account_id,
                debit_amount=entry.credit_amount,
                credit_amount=entry.debit_amount,
                description=f"Reversal: {entry.description}"
            )
            self.db.add(reversal_entry)
        
        # Mark original transaction as reversed
        original_transaction.is_reversed = True
        
        # Post the reversal transaction
        await self.db.flush()
        await self.post_transaction(reversal.id)
        
        await self.db.refresh(reversal)
        return reversal
    
    async def get_journal_entries(
        self,
        company_id: int,
        skip: int = 0,
        limit: int = 50,
        start_date: Optional[date] = None,
        end_date: Optional[date] = None,
        account_id: Optional[int] = None
    ) -> List[Dict[str, Any]]:
        """Get journal entries (general ledger)."""
        query = select(TransactionEntry).join(Transaction).join(Account).where(
            and_(
                Transaction.company_id == company_id,
                Transaction.is_posted == True,
                Transaction.is_deleted == False
            )
        )
        
        # Apply filters
        if start_date:
            query = query.where(Transaction.transaction_date >= start_date)
        
        if end_date:
            query = query.where(Transaction.transaction_date <= end_date)
        
        if account_id:
            query = query.where(TransactionEntry.account_id == account_id)
        
        # Apply ordering and pagination
        query = query.order_by(
            Transaction.transaction_date.desc(),
            Transaction.id.desc(),
            TransactionEntry.id
        ).offset(skip).limit(limit)
        
        result = await self.db.execute(query)
        entries = result.scalars().all()
        
        # Format entries for response
        journal_entries = []
        for entry in entries:
            journal_entries.append({
                "id": entry.id,
                "transaction_id": entry.transaction_id,
                "transaction_reference": entry.transaction.reference,
                "transaction_date": entry.transaction.transaction_date,
                "account_id": entry.account_id,
                "account_code": entry.account.code,
                "account_name": entry.account.name,
                "description": entry.description or entry.transaction.description,
                "debit_amount": float(entry.debit_amount),
                "credit_amount": float(entry.credit_amount),
                "amount": float(entry.amount),
                "is_debit": entry.is_debit
            })
        
        return journal_entries
