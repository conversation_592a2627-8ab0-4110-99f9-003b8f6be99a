"""
Customer model for CRM functionality.
"""

from sqlalchemy import <PERSON>umn, Integer, String, Text, Boolean, ForeignKey, Numeric, Date
from sqlalchemy.orm import relationship
from decimal import Decimal

from .base import BaseModel


class Customer(BaseModel):
    """Customer model."""
    
    __tablename__ = "customers"
    
    # Basic information
    customer_code = Column(String(50), nullable=False, index=True)
    name = Column(String(255), nullable=False, index=True)
    company_name = Column(String(255), nullable=True)
    
    # Contact information
    email = Column(String(255), nullable=True)
    phone = Column(String(20), nullable=True)
    mobile = Column(String(20), nullable=True)
    website = Column(String(255), nullable=True)
    
    # Address
    billing_address_line1 = Column(String(255), nullable=True)
    billing_address_line2 = Column(String(255), nullable=True)
    billing_city = Column(String(100), nullable=True)
    billing_state = Column(String(100), nullable=True)
    billing_postal_code = Column(String(20), nullable=True)
    billing_country = Column(String(100), nullable=True)
    
    shipping_address_line1 = Column(String(255), nullable=True)
    shipping_address_line2 = Column(String(255), nullable=True)
    shipping_city = Column(String(100), nullable=True)
    shipping_state = Column(String(100), nullable=True)
    shipping_postal_code = Column(String(20), nullable=True)
    shipping_country = Column(String(100), nullable=True)
    
    # Business information
    tax_id = Column(String(100), nullable=True)
    registration_number = Column(String(100), nullable=True)
    
    # Financial settings
    credit_limit = Column(Numeric(15, 2), default=0, nullable=False)
    payment_terms_days = Column(Integer, default=30, nullable=False)
    currency = Column(String(3), default="USD", nullable=False)
    
    # Status
    is_active = Column(Boolean, default=True, nullable=False)
    
    # Notes
    notes = Column(Text, nullable=True)
    
    # Company association
    company_id = Column(Integer, ForeignKey("companies.id"), nullable=False)
    
    # Relationships
    company = relationship("Company", back_populates="customers")
    invoices = relationship("Invoice", back_populates="customer")
    
    @property
    def full_billing_address(self) -> str:
        """Get formatted billing address."""
        address_parts = [
            self.billing_address_line1,
            self.billing_address_line2,
            self.billing_city,
            self.billing_state,
            self.billing_postal_code,
            self.billing_country
        ]
        return ", ".join(filter(None, address_parts))
    
    @property
    def full_shipping_address(self) -> str:
        """Get formatted shipping address."""
        address_parts = [
            self.shipping_address_line1,
            self.shipping_address_line2,
            self.shipping_city,
            self.shipping_state,
            self.shipping_postal_code,
            self.shipping_country
        ]
        return ", ".join(filter(None, address_parts))
    
    @property
    def display_name(self) -> str:
        """Get display name for customer."""
        if self.company_name:
            return f"{self.company_name} ({self.name})"
        return self.name
    
    def get_outstanding_balance(self) -> Decimal:
        """Calculate outstanding balance from unpaid invoices."""
        outstanding = Decimal('0')
        for invoice in self.invoices:
            if invoice.status in ['sent', 'overdue']:
                outstanding += invoice.total_amount - invoice.paid_amount
        return outstanding
    
    def is_credit_limit_exceeded(self, additional_amount: Decimal = Decimal('0')) -> bool:
        """Check if credit limit would be exceeded."""
        if self.credit_limit <= 0:
            return False  # No credit limit set
        
        current_outstanding = self.get_outstanding_balance()
        return (current_outstanding + additional_amount) > self.credit_limit
    
    def get_overdue_invoices(self):
        """Get list of overdue invoices."""
        return [invoice for invoice in self.invoices if invoice.is_overdue()]
