"""
Product and ProductCategory models for inventory management.
"""

from sqlalchemy import Column, Integer, String, Text, Boolean, ForeignKey, Numeric, Enum
from sqlalchemy.orm import relationship
from enum import Enum as PyEnum
from decimal import Decimal

from .base import BaseModel


class ProductType(PyEnum):
    """Product types."""
    PRODUCT = "product"
    SERVICE = "service"


class Product(BaseModel):
    """Product model."""
    
    __tablename__ = "products"
    
    # Basic information
    sku = Column(String(100), nullable=False, index=True)
    name = Column(String(255), nullable=False, index=True)
    description = Column(Text, nullable=True)
    
    # Product type
    product_type = Column(Enum(ProductType), default=ProductType.PRODUCT, nullable=False)
    
    # Category
    category_id = Column(Integer, ForeignKey("product_categories.id"), nullable=True)
    
    # Pricing
    unit_price = Column(Numeric(15, 2), default=0, nullable=False)
    cost_price = Column(Numeric(15, 2), default=0, nullable=False)
    
    # Inventory tracking (only for products, not services)
    track_inventory = Column(Boolean, default=True, nullable=False)
    current_stock = Column(Numeric(15, 3), default=0, nullable=False)
    minimum_stock = Column(Numeric(15, 3), default=0, nullable=False)
    maximum_stock = Column(Numeric(15, 3), default=0, nullable=True)
    
    # Units
    unit_of_measure = Column(String(20), default="pcs", nullable=False)
    
    # Tax
    tax_rate = Column(Numeric(5, 2), default=0, nullable=False)
    is_tax_inclusive = Column(Boolean, default=False, nullable=False)
    
    # Status
    is_active = Column(Boolean, default=True, nullable=False)
    is_for_sale = Column(Boolean, default=True, nullable=False)
    is_for_purchase = Column(Boolean, default=True, nullable=False)
    
    # Additional information
    barcode = Column(String(100), nullable=True, index=True)
    brand = Column(String(100), nullable=True)
    model = Column(String(100), nullable=True)
    weight = Column(Numeric(10, 3), nullable=True)
    dimensions = Column(String(100), nullable=True)
    
    # Images
    image_url = Column(String(500), nullable=True)
    
    # Company association
    company_id = Column(Integer, ForeignKey("companies.id"), nullable=False)
    
    # Relationships
    company = relationship("Company", back_populates="products")
    category = relationship("ProductCategory", back_populates="products")
    inventory_items = relationship("InventoryItem", back_populates="product")
    invoice_items = relationship("InvoiceItem", back_populates="product")
    
    @property
    def is_low_stock(self) -> bool:
        """Check if product is low on stock."""
        if not self.track_inventory or self.product_type == ProductType.SERVICE:
            return False
        return self.current_stock <= self.minimum_stock
    
    @property
    def is_out_of_stock(self) -> bool:
        """Check if product is out of stock."""
        if not self.track_inventory or self.product_type == ProductType.SERVICE:
            return False
        return self.current_stock <= 0
    
    @property
    def profit_margin(self) -> Decimal:
        """Calculate profit margin percentage."""
        if self.cost_price == 0:
            return Decimal('0')
        return ((self.unit_price - self.cost_price) / self.cost_price) * 100
    
    def update_stock(self, quantity: Decimal, operation: str = "add") -> None:
        """Update product stock."""
        if not self.track_inventory:
            return
        
        if operation == "add":
            self.current_stock += quantity
        elif operation == "subtract":
            self.current_stock -= quantity
        else:
            self.current_stock = quantity
    
    def can_fulfill_quantity(self, quantity: Decimal) -> bool:
        """Check if product can fulfill the requested quantity."""
        if not self.track_inventory or self.product_type == ProductType.SERVICE:
            return True
        return self.current_stock >= quantity


class ProductCategory(BaseModel):
    """Product category model."""
    
    __tablename__ = "product_categories"
    
    # Basic information
    name = Column(String(255), nullable=False, index=True)
    description = Column(Text, nullable=True)
    
    # Hierarchy
    parent_id = Column(Integer, ForeignKey("product_categories.id"), nullable=True)
    
    # Status
    is_active = Column(Boolean, default=True, nullable=False)
    
    # Company association
    company_id = Column(Integer, ForeignKey("companies.id"), nullable=False)
    
    # Relationships
    company = relationship("Company")
    parent = relationship("ProductCategory", remote_side=[id], backref="children")
    products = relationship("Product", back_populates="category")
    
    @property
    def full_name(self) -> str:
        """Get full category name including parent names."""
        if self.parent:
            return f"{self.parent.full_name} > {self.name}"
        return self.name
