"""
Account model for chart of accounts.
"""

from sqlalchemy import <PERSON>umn, Integer, String, Text, Boolean, Enum, ForeignKey, Numeric
from sqlalchemy.orm import relationship
from enum import Enum as PyEnum
from decimal import Decimal

from .base import BaseModel


class AccountType(PyEnum):
    """Account types in the chart of accounts."""
    ASSET = "asset"
    LIABILITY = "liability"
    EQUITY = "equity"
    REVENUE = "revenue"
    EXPENSE = "expense"


class Account(BaseModel):
    """Account model for chart of accounts."""
    
    __tablename__ = "accounts"
    
    # Basic information
    code = Column(String(20), nullable=False, index=True)
    name = Column(String(255), nullable=False)
    description = Column(Text, nullable=True)
    
    # Account classification
    account_type = Column(Enum(AccountType), nullable=False)
    
    # Hierarchy
    parent_id = Column(Integer, ForeignKey("accounts.id"), nullable=True)
    level = Column(Integer, default=0, nullable=False)
    
    # Properties
    is_active = Column(Boolean, default=True, nullable=False)
    is_system = Column(Boolean, default=False, nullable=False)  # System accounts cannot be deleted
    allow_manual_entries = Column(Boolean, default=True, nullable=False)
    
    # Balance tracking
    current_balance = Column(Numeric(15, 2), default=0, nullable=False)
    
    # Company association
    company_id = Column(Integer, ForeignKey("companies.id"), nullable=False)
    
    # Relationships
    company = relationship("Company", back_populates="accounts")
    parent = relationship("Account", remote_side=[id], backref="children")
    transaction_entries = relationship("TransactionEntry", back_populates="account")
    
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        if self.parent_id:
            # Set level based on parent
            if self.parent:
                self.level = self.parent.level + 1
    
    @property
    def full_code(self) -> str:
        """Get full account code including parent codes."""
        if self.parent:
            return f"{self.parent.full_code}.{self.code}"
        return self.code
    
    @property
    def full_name(self) -> str:
        """Get full account name including parent names."""
        if self.parent:
            return f"{self.parent.full_name} > {self.name}"
        return self.name
    
    def is_debit_account(self) -> bool:
        """Check if this is a debit account (Assets, Expenses)."""
        return self.account_type in [AccountType.ASSET, AccountType.EXPENSE]
    
    def is_credit_account(self) -> bool:
        """Check if this is a credit account (Liabilities, Equity, Revenue)."""
        return self.account_type in [AccountType.LIABILITY, AccountType.EQUITY, AccountType.REVENUE]
    
    def update_balance(self, amount: Decimal, is_debit: bool) -> None:
        """Update account balance based on transaction."""
        if self.is_debit_account():
            # For debit accounts: debit increases, credit decreases
            if is_debit:
                self.current_balance += amount
            else:
                self.current_balance -= amount
        else:
            # For credit accounts: credit increases, debit decreases
            if is_debit:
                self.current_balance -= amount
            else:
                self.current_balance += amount
    
    def get_balance_display(self) -> str:
        """Get formatted balance for display."""
        if self.current_balance >= 0:
            return f"{self.current_balance:,.2f}"
        else:
            return f"({abs(self.current_balance):,.2f})"
    
    def can_be_deleted(self) -> bool:
        """Check if account can be deleted."""
        if self.is_system:
            return False
        if self.current_balance != 0:
            return False
        if self.children:
            return False
        if self.transaction_entries:
            return False
        return True
