# Application Settings
APP_NAME=Global Accounting System
APP_VERSION=1.0.0
DEBUG=False

# Security
SECRET_KEY=GlobalAccounting2024SecretKey!@#$%^&*()
ACCESS_TOKEN_EXPIRE_MINUTES=30
REFRESH_TOKEN_EXPIRE_DAYS=7

# Database (Supabase)
SUPABASE_URL=https://unyoxxghnsllcpaxplvk.supabase.co
SUPABASE_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8
SUPABASE_SERVICE_KEY=your-supabase-service-key

# Database Connection
DATABASE_URL=postgresql+asyncpg://postgres.unyoxxghnsllcpaxplvk:GlobalAccounting2024!@aws-0-eu-north-1.pooler.supabase.com:6543/postgres

# Redis (استخدم خدمة Redis مجانية مثل Upstash)
REDIS_URL=redis://localhost:6379

# Email
SMTP_TLS=True
SMTP_PORT=587
SMTP_HOST=smtp.gmail.com
SMTP_USER=<EMAIL>
SMTP_PASSWORD=your-app-password
EMAILS_FROM_EMAIL=<EMAIL>
EMAILS_FROM_NAME=Global Accounting System

# CORS
ALLOWED_HOSTS=["http://localhost:3000", "https://global-accounting-frontend.vercel.app"]

# File Upload
UPLOAD_DIR=uploads
MAX_FILE_SIZE=********

# Currency
DEFAULT_CURRENCY=SAR
EXCHANGE_RATE_API_KEY=your-exchange-rate-api-key

# Rate Limiting
RATE_LIMIT_PER_MINUTE=60
