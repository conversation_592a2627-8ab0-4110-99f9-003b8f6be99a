# Application Settings
APP_NAME=Global Accounting System
APP_VERSION=1.0.0
DEBUG=False

# Security
SECRET_KEY=your-super-secret-key-change-this-in-production
ACCESS_TOKEN_EXPIRE_MINUTES=30
REFRESH_TOKEN_EXPIRE_DAYS=7

# Database (Supabase)
SUPABASE_URL=your-supabase-url
SUPABASE_KEY=your-supabase-anon-key
SUPABASE_SERVICE_KEY=your-supabase-service-key

# Or use local PostgreSQL
# DATABASE_URL=postgresql+asyncpg://username:password@localhost:5432/accounting_db

# Redis
REDIS_URL=redis://localhost:6379

# Email
SMTP_TLS=True
SMTP_PORT=587
SMTP_HOST=smtp.gmail.com
SMTP_USER=<EMAIL>
SMTP_PASSWORD=your-app-password
EMAILS_FROM_EMAIL=<EMAIL>
EMAILS_FROM_NAME=Global Accounting System

# CORS
ALLOWED_HOSTS=["http://localhost:3000", "https://yourdomain.com"]

# File Upload
UPLOAD_DIR=uploads
MAX_FILE_SIZE=********

# Currency
DEFAULT_CURRENCY=USD
EXCHANGE_RATE_API_KEY=your-exchange-rate-api-key

# Rate Limiting
RATE_LIMIT_PER_MINUTE=60
