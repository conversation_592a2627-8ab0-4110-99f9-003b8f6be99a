"""
Transaction and TransactionEntry models for double-entry bookkeeping.
"""

from sqlalchemy import Column, Integer, String, Text, Date, ForeignKey, Numeric, Boolean
from sqlalchemy.orm import relationship
from decimal import Decimal
from datetime import date

from .base import BaseModel


class Transaction(BaseModel):
    """Transaction model for journal entries."""
    
    __tablename__ = "transactions"
    
    # Basic information
    reference = Column(String(100), nullable=False, index=True)
    description = Column(Text, nullable=False)
    transaction_date = Column(Date, nullable=False, index=True)
    
    # Status
    is_posted = Column(Boolean, default=False, nullable=False)
    is_reversed = Column(Boolean, default=False, nullable=False)
    
    # References
    source_type = Column(String(50), nullable=True)  # invoice, payment, manual, etc.
    source_id = Column(Integer, nullable=True)
    
    # Company association
    company_id = Column(Integer, ForeignKey("companies.id"), nullable=False)
    
    # User who created the transaction
    created_by_id = <PERSON>umn(Integer, ForeignKey("users.id"), nullable=False)
    
    # Relationships
    company = relationship("Company", back_populates="transactions")
    created_by = relationship("User")
    entries = relationship("TransactionEntry", back_populates="transaction", cascade="all, delete-orphan")
    
    @property
    def total_debits(self) -> Decimal:
        """Calculate total debit amount."""
        return sum(entry.debit_amount for entry in self.entries)
    
    @property
    def total_credits(self) -> Decimal:
        """Calculate total credit amount."""
        return sum(entry.credit_amount for entry in self.entries)
    
    @property
    def is_balanced(self) -> bool:
        """Check if transaction is balanced (debits = credits)."""
        return self.total_debits == self.total_credits
    
    def post(self) -> None:
        """Post the transaction (update account balances)."""
        if self.is_posted:
            raise ValueError("Transaction is already posted")
        
        if not self.is_balanced:
            raise ValueError("Transaction is not balanced")
        
        # Update account balances
        for entry in self.entries:
            if entry.debit_amount > 0:
                entry.account.update_balance(entry.debit_amount, True)
            if entry.credit_amount > 0:
                entry.account.update_balance(entry.credit_amount, False)
        
        self.is_posted = True
    
    def reverse(self, reversal_date: date, description: str = None) -> 'Transaction':
        """Create a reversal transaction."""
        if not self.is_posted:
            raise ValueError("Cannot reverse unposted transaction")
        
        if self.is_reversed:
            raise ValueError("Transaction is already reversed")
        
        # Create reversal transaction
        reversal = Transaction(
            reference=f"REV-{self.reference}",
            description=description or f"Reversal of {self.description}",
            transaction_date=reversal_date,
            company_id=self.company_id,
            created_by_id=self.created_by_id,
            source_type="reversal",
            source_id=self.id
        )
        
        # Create reversal entries (swap debits and credits)
        for entry in self.entries:
            reversal_entry = TransactionEntry(
                account_id=entry.account_id,
                debit_amount=entry.credit_amount,
                credit_amount=entry.debit_amount,
                description=f"Reversal: {entry.description}"
            )
            reversal.entries.append(reversal_entry)
        
        self.is_reversed = True
        return reversal


class TransactionEntry(BaseModel):
    """Individual entries within a transaction."""
    
    __tablename__ = "transaction_entries"
    
    # Transaction reference
    transaction_id = Column(Integer, ForeignKey("transactions.id"), nullable=False)
    
    # Account reference
    account_id = Column(Integer, ForeignKey("accounts.id"), nullable=False)
    
    # Amounts (one should be zero, the other positive)
    debit_amount = Column(Numeric(15, 2), default=0, nullable=False)
    credit_amount = Column(Numeric(15, 2), default=0, nullable=False)
    
    # Description for this specific entry
    description = Column(Text, nullable=True)
    
    # Relationships
    transaction = relationship("Transaction", back_populates="entries")
    account = relationship("Account", back_populates="transaction_entries")
    
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        # Ensure only one amount is set
        if self.debit_amount > 0 and self.credit_amount > 0:
            raise ValueError("Entry cannot have both debit and credit amounts")
        if self.debit_amount == 0 and self.credit_amount == 0:
            raise ValueError("Entry must have either debit or credit amount")
    
    @property
    def amount(self) -> Decimal:
        """Get the entry amount (positive for both debit and credit)."""
        return max(self.debit_amount, self.credit_amount)
    
    @property
    def is_debit(self) -> bool:
        """Check if this is a debit entry."""
        return self.debit_amount > 0
    
    @property
    def is_credit(self) -> bool:
        """Check if this is a credit entry."""
        return self.credit_amount > 0
