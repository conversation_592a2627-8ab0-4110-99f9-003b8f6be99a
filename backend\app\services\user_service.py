"""
User service for user-related operations.
"""

from typing import Optional, List
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select

from app.models.user import User
from app.schemas.user import UserCreate, UserUpdate
from app.services.base_service import BaseService


class UserService(BaseService[User, UserCreate, UserUpdate]):
    """User service class."""
    
    def __init__(self, db: AsyncSession):
        super().__init__(User, db)
    
    async def get_by_email(self, email: str) -> Optional[User]:
        """Get user by email."""
        result = await self.db.execute(
            select(User).where(
                User.email == email,
                User.is_deleted == False
            )
        )
        return result.scalar_one_or_none()
    
    async def get_by_username(self, username: str) -> Optional[User]:
        """Get user by username."""
        result = await self.db.execute(
            select(User).where(
                User.username == username,
                User.is_deleted == False
            )
        )
        return result.scalar_one_or_none()
    
    async def get_by_company(self, company_id: int) -> List[User]:
        """Get all users in a company."""
        result = await self.db.execute(
            select(User).where(
                User.company_id == company_id,
                User.is_deleted == False
            ).order_by(User.first_name, User.last_name)
        )
        return result.scalars().all()
    
    async def activate_user(self, user_id: int) -> Optional[User]:
        """Activate a user."""
        return await self.update(user_id, {"is_active": True})
    
    async def deactivate_user(self, user_id: int) -> Optional[User]:
        """Deactivate a user."""
        return await self.update(user_id, {"is_active": False})
    
    async def verify_user(self, user_id: int) -> Optional[User]:
        """Verify a user."""
        return await self.update(user_id, {"is_verified": True})
    
    async def change_role(self, user_id: int, new_role: str) -> Optional[User]:
        """Change user role."""
        return await self.update(user_id, {"role": new_role})
    
    async def search_users(self, query: str, company_id: Optional[int] = None) -> List[User]:
        """Search users by name, email, or username."""
        search_filter = select(User).where(
            User.is_deleted == False
        ).where(
            (User.first_name.ilike(f"%{query}%")) |
            (User.last_name.ilike(f"%{query}%")) |
            (User.email.ilike(f"%{query}%")) |
            (User.username.ilike(f"%{query}%"))
        )
        
        if company_id:
            search_filter = search_filter.where(User.company_id == company_id)
        
        result = await self.db.execute(search_filter.order_by(User.first_name))
        return result.scalars().all()
