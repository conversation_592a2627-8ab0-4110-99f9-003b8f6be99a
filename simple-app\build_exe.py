"""
سكريبت بناء ملف EXE لنظام المحاسبة العالمي
Build script for Global Accounting System EXE
"""

import os
import sys
import subprocess
import shutil
from pathlib import Path

def create_spec_file():
    """إنشاء ملف spec مخصص"""
    spec_content = '''
# -*- mode: python ; coding: utf-8 -*-

block_cipher = None

a = Analysis(
    ['main.py'],
    pathex=[],
    binaries=[],
    datas=[('static', 'static')],
    hiddenimports=[
        'uvicorn.lifespan.on',
        'uvicorn.lifespan.off',
        'uvicorn.protocols.websockets.auto',
        'uvicorn.protocols.http.auto',
        'uvicorn.protocols.websockets.websockets_impl',
        'uvicorn.protocols.http.h11_impl',
        'uvicorn.protocols.http.httptools_impl',
        'uvicorn.protocols.websockets.wsproto_impl',
        'uvicorn.loops.auto',
        'uvicorn.loops.asyncio',
        'uvicorn.loops.uvloop',
        'fastapi.applications',
        'starlette.applications',
        'starlette.middleware',
        'starlette.routing',
        'starlette.responses',
        'starlette.staticfiles',
        'pydantic',
        'jwt',
        'json',
        'datetime'
    ],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='GlobalAccountingSystem',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=True,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon='icon.ico' if os.path.exists('icon.ico') else None,
)
'''
    
    with open('GlobalAccountingSystem.spec', 'w', encoding='utf-8') as f:
        f.write(spec_content)
    
    print("✅ تم إنشاء ملف spec")

def create_icon():
    """إنشاء أيقونة بسيطة"""
    # سنتخطى الأيقونة للآن
    print("⏭️ تخطي إنشاء الأيقونة")

def build_exe():
    """بناء ملف EXE"""
    print("🔨 بدء بناء ملف EXE...")
    
    try:
        # تنظيف الملفات السابقة
        if os.path.exists('dist'):
            shutil.rmtree('dist')
        if os.path.exists('build'):
            shutil.rmtree('build')
        
        # بناء EXE
        result = subprocess.run([
            'pyinstaller', 
            '--onefile',
            '--add-data', 'static;static',
            '--hidden-import', 'uvicorn.lifespan.on',
            '--hidden-import', 'uvicorn.protocols.websockets.auto',
            '--hidden-import', 'uvicorn.protocols.http.auto',
            '--hidden-import', 'fastapi.applications',
            '--hidden-import', 'starlette.applications',
            '--name', 'GlobalAccountingSystem',
            '--console',
            'main.py'
        ], capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✅ تم بناء ملف EXE بنجاح!")
            
            # التحقق من وجود الملف
            exe_path = Path('dist/GlobalAccountingSystem.exe')
            if exe_path.exists():
                size_mb = exe_path.stat().st_size / (1024 * 1024)
                print(f"📁 مسار الملف: {exe_path.absolute()}")
                print(f"📊 حجم الملف: {size_mb:.1f} MB")
                return str(exe_path.absolute())
            else:
                print("❌ لم يتم العثور على ملف EXE")
                return None
        else:
            print("❌ فشل في بناء ملف EXE")
            print("خطأ:", result.stderr)
            return None
            
    except Exception as e:
        print(f"❌ خطأ في بناء EXE: {e}")
        return None

def create_batch_file(exe_path):
    """إنشاء ملف batch لتشغيل النظام"""
    batch_content = f'''@echo off
title نظام المحاسبة العالمي - Global Accounting System
echo.
echo ================================================
echo    نظام المحاسبة العالمي
echo    Global Accounting System
echo ================================================
echo.
echo جاري تشغيل النظام...
echo Starting the system...
echo.

"{exe_path}"

echo.
echo تم إغلاق النظام
echo System closed
pause
'''
    
    batch_path = Path('dist/تشغيل_النظام.bat')
    with open(batch_path, 'w', encoding='utf-8') as f:
        f.write(batch_content)
    
    print(f"✅ تم إنشاء ملف التشغيل: {batch_path}")

def create_readme():
    """إنشاء ملف README للتوزيع"""
    readme_content = '''# نظام المحاسبة العالمي - Global Accounting System

## 🚀 تشغيل النظام

### الطريقة الأولى:
1. انقر نقراً مزدوجاً على ملف "GlobalAccountingSystem.exe"
2. انتظر حتى يبدأ النظام (قد يستغرق دقيقة)
3. افتح المتصفح واذهب إلى: http://localhost:8000

### الطريقة الثانية:
1. انقر نقراً مزدوجاً على ملف "تشغيل_النظام.bat"
2. سيتم تشغيل النظام تلقائياً
3. افتح المتصفح واذهب إلى: http://localhost:8000

## 🔑 بيانات تسجيل الدخول

**اسم المستخدم**: admin
**كلمة المرور**: admin123

## 🌐 الروابط المهمة

- الصفحة الرئيسية: http://localhost:8000
- وثائق API: http://localhost:8000/docs
- فحص الصحة: http://localhost:8000/health

## 📱 الميزات

✅ نظام مصادقة آمن
✅ لوحة تحكم تفاعلية
✅ إدارة الشركات
✅ دليل الحسابات
✅ إحصائيات فورية
✅ واجهة عربية/إنجليزية

## 🛠️ متطلبات النظام

- Windows 7/8/10/11
- لا يحتاج تثبيت Python
- يعمل مباشرة بدون إعداد

## 🆘 الدعم

للمساعدة: <EMAIL>

---
تم تطوير هذا النظام بواسطة فريق التطوير العالمي
'''
    
    readme_path = Path('dist/اقرأني - README.txt')
    with open(readme_path, 'w', encoding='utf-8') as f:
        f.write(readme_content)
    
    print(f"✅ تم إنشاء ملف README: {readme_path}")

def main():
    """الدالة الرئيسية"""
    print("🚀 بدء عملية إنشاء ملف EXE لنظام المحاسبة العالمي")
    print("=" * 60)
    
    # التحقق من وجود الملفات المطلوبة
    if not os.path.exists('main.py'):
        print("❌ ملف main.py غير موجود")
        return
    
    if not os.path.exists('static'):
        print("❌ مجلد static غير موجود")
        return
    
    # إنشاء الأيقونة
    create_icon()
    
    # بناء EXE
    exe_path = build_exe()
    
    if exe_path:
        # إنشاء ملفات إضافية
        create_batch_file(exe_path)
        create_readme()
        
        print("\n" + "=" * 60)
        print("🎉 تم إنشاء ملف EXE بنجاح!")
        print("=" * 60)
        print(f"📁 مجلد التوزيع: {Path('dist').absolute()}")
        print(f"🖥️ ملف EXE: {exe_path}")
        print(f"🚀 ملف التشغيل: dist/تشغيل_النظام.bat")
        print(f"📖 ملف التعليمات: dist/اقرأني - README.txt")
        print("\n✅ النظام جاهز للتوزيع والنشر!")
    else:
        print("❌ فشل في إنشاء ملف EXE")

if __name__ == "__main__":
    main()
