version: '3.8'

services:
  # Backend API
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    container_name: accounting_backend_prod
    environment:
      - DATABASE_URL=postgresql+asyncpg://postgres.unyoxxghnsllcpaxplvk:GlobalAccounting2024!@aws-0-eu-north-1.pooler.supabase.com:6543/postgres
      - SECRET_KEY=GlobalAccounting2024SecretKey!@#$%^&*()
      - SUPABASE_URL=https://unyoxxghnsllcpaxplvk.supabase.co
      - SUPABASE_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8
      - DEBUG=False
      - REDIS_URL=redis://redis:6379
    ports:
      - "8000:8000"
    depends_on:
      - redis
    networks:
      - accounting_network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Frontend
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
    container_name: accounting_frontend_prod
    environment:
      - NEXT_PUBLIC_API_URL=https://global-accounting-backend.railway.app
      - NEXT_PUBLIC_APP_NAME=نظام المحاسبة العالمي
      - NODE_ENV=production
    ports:
      - "3000:3000"
    depends_on:
      - backend
    networks:
      - accounting_network
    restart: unless-stopped

  # Redis for caching
  redis:
    image: redis:7-alpine
    container_name: accounting_redis_prod
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - accounting_network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Nginx Reverse Proxy
  nginx:
    image: nginx:alpine
    container_name: accounting_nginx_prod
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./deployment/nginx/nginx.conf:/etc/nginx/nginx.conf
      - ./deployment/nginx/ssl:/etc/nginx/ssl
    depends_on:
      - frontend
      - backend
    networks:
      - accounting_network
    restart: unless-stopped

volumes:
  redis_data:

networks:
  accounting_network:
    driver: bridge
