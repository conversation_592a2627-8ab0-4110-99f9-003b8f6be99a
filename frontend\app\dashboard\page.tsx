'use client'

import { useQuery } from '@tanstack/react-query'
import {
  CurrencyDollarIcon,
  DocumentTextIcon,
  UsersIcon,
  ChartBarIcon,
  ArrowUpIcon,
  ArrowDownIcon,
} from '@heroicons/react/24/outline'
import { dashboardApi } from '@/lib/api'

const StatCard = ({ title, value, change, changeType, icon: Icon }: any) => (
  <div className="bg-white overflow-hidden shadow rounded-lg">
    <div className="p-5">
      <div className="flex items-center">
        <div className="flex-shrink-0">
          <Icon className="h-6 w-6 text-gray-400" />
        </div>
        <div className="ml-5 w-0 flex-1">
          <dl>
            <dt className="text-sm font-medium text-gray-500 truncate">{title}</dt>
            <dd className="text-lg font-medium text-gray-900">{value}</dd>
          </dl>
        </div>
      </div>
    </div>
    <div className="bg-gray-50 px-5 py-3">
      <div className="text-sm">
        <span className={`flex items-center ${
          changeType === 'increase' ? 'text-green-600' : 'text-red-600'
        }`}>
          {changeType === 'increase' ? (
            <ArrowUpIcon className="h-4 w-4 mr-1" />
          ) : (
            <ArrowDownIcon className="h-4 w-4 mr-1" />
          )}
          {change}
        </span>
      </div>
    </div>
  </div>
)

const RecentActivityItem = ({ activity }: any) => (
  <li className="py-4">
    <div className="flex space-x-3">
      <div className="flex-1 space-y-1">
        <div className="flex items-center justify-between">
          <h3 className="text-sm font-medium">{activity.title}</h3>
          <p className="text-sm text-gray-500">{activity.time}</p>
        </div>
        <p className="text-sm text-gray-500">{activity.description}</p>
      </div>
    </div>
  </li>
)

export default function DashboardPage() {
  // Fetch dashboard stats
  const { data: stats, isLoading: statsLoading } = useQuery({
    queryKey: ['dashboardStats'],
    queryFn: dashboardApi.getStats,
  })

  // Fetch recent activity
  const { data: activity, isLoading: activityLoading } = useQuery({
    queryKey: ['recentActivity'],
    queryFn: dashboardApi.getRecentActivity,
  })

  if (statsLoading) {
    return (
      <div className="animate-pulse">
        <div className="grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4">
          {[...Array(4)].map((_, i) => (
            <div key={i} className="bg-white overflow-hidden shadow rounded-lg">
              <div className="p-5">
                <div className="h-16 bg-gray-200 rounded"></div>
              </div>
            </div>
          ))}
        </div>
      </div>
    )
  }

  const mockStats = stats || {
    totalRevenue: '$125,430',
    totalExpenses: '$89,240',
    netProfit: '$36,190',
    outstandingInvoices: '$23,450',
    revenueChange: '+12.5%',
    expensesChange: '+8.2%',
    profitChange: '+18.7%',
    invoicesChange: '-5.3%',
  }

  const mockActivity = activity || [
    {
      id: 1,
      title: 'New invoice created',
      description: 'Invoice #INV-2024-0001 for $2,500 created for ABC Corp',
      time: '2 hours ago',
    },
    {
      id: 2,
      title: 'Payment received',
      description: 'Payment of $1,200 received from XYZ Ltd',
      time: '4 hours ago',
    },
    {
      id: 3,
      title: 'New customer added',
      description: 'Customer "Tech Solutions Inc" added to the system',
      time: '6 hours ago',
    },
    {
      id: 4,
      title: 'Expense recorded',
      description: 'Office supplies expense of $350 recorded',
      time: '1 day ago',
    },
  ]

  return (
    <div>
      {/* Page header */}
      <div className="md:flex md:items-center md:justify-between">
        <div className="flex-1 min-w-0">
          <h2 className="text-2xl font-bold leading-7 text-gray-900 sm:text-3xl sm:truncate">
            Dashboard
          </h2>
        </div>
        <div className="mt-4 flex md:mt-0 md:ml-4">
          <button
            type="button"
            className="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
          >
            Export Report
          </button>
          <button
            type="button"
            className="ml-3 inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
          >
            Create Invoice
          </button>
        </div>
      </div>

      {/* Stats */}
      <div className="mt-8">
        <div className="grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4">
          <StatCard
            title="Total Revenue"
            value={mockStats.totalRevenue}
            change={mockStats.revenueChange}
            changeType="increase"
            icon={CurrencyDollarIcon}
          />
          <StatCard
            title="Total Expenses"
            value={mockStats.totalExpenses}
            change={mockStats.expensesChange}
            changeType="increase"
            icon={ChartBarIcon}
          />
          <StatCard
            title="Net Profit"
            value={mockStats.netProfit}
            change={mockStats.profitChange}
            changeType="increase"
            icon={ArrowUpIcon}
          />
          <StatCard
            title="Outstanding Invoices"
            value={mockStats.outstandingInvoices}
            change={mockStats.invoicesChange}
            changeType="decrease"
            icon={DocumentTextIcon}
          />
        </div>
      </div>

      {/* Charts and Recent Activity */}
      <div className="mt-8 grid grid-cols-1 gap-8 lg:grid-cols-2">
        {/* Revenue Chart */}
        <div className="bg-white shadow rounded-lg">
          <div className="px-4 py-5 sm:p-6">
            <h3 className="text-lg leading-6 font-medium text-gray-900">Revenue Overview</h3>
            <div className="mt-5">
              <div className="h-64 flex items-center justify-center bg-gray-50 rounded-lg">
                <p className="text-gray-500">Chart will be implemented with Chart.js</p>
              </div>
            </div>
          </div>
        </div>

        {/* Recent Activity */}
        <div className="bg-white shadow rounded-lg">
          <div className="px-4 py-5 sm:p-6">
            <h3 className="text-lg leading-6 font-medium text-gray-900">Recent Activity</h3>
            <div className="mt-5">
              <div className="flow-root">
                <ul className="-mb-8">
                  {mockActivity.map((item, itemIdx) => (
                    <li key={item.id}>
                      <div className="relative pb-8">
                        {itemIdx !== mockActivity.length - 1 ? (
                          <span
                            className="absolute top-5 left-5 -ml-px h-full w-0.5 bg-gray-200"
                            aria-hidden="true"
                          />
                        ) : null}
                        <div className="relative flex items-start space-x-3">
                          <div className="relative">
                            <div className="h-10 w-10 rounded-full bg-primary-500 flex items-center justify-center ring-8 ring-white">
                              <DocumentTextIcon className="h-5 w-5 text-white" />
                            </div>
                          </div>
                          <div className="min-w-0 flex-1">
                            <div>
                              <div className="text-sm">
                                <span className="font-medium text-gray-900">{item.title}</span>
                              </div>
                              <p className="mt-0.5 text-sm text-gray-500">{item.time}</p>
                            </div>
                            <div className="mt-2 text-sm text-gray-700">
                              <p>{item.description}</p>
                            </div>
                          </div>
                        </div>
                      </div>
                    </li>
                  ))}
                </ul>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Quick Actions */}
      <div className="mt-8">
        <div className="bg-white shadow rounded-lg">
          <div className="px-4 py-5 sm:p-6">
            <h3 className="text-lg leading-6 font-medium text-gray-900">Quick Actions</h3>
            <div className="mt-5 grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-4">
              <button className="relative block w-full border-2 border-gray-300 border-dashed rounded-lg p-6 text-center hover:border-gray-400 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500">
                <DocumentTextIcon className="mx-auto h-8 w-8 text-gray-400" />
                <span className="mt-2 block text-sm font-medium text-gray-900">Create Invoice</span>
              </button>
              
              <button className="relative block w-full border-2 border-gray-300 border-dashed rounded-lg p-6 text-center hover:border-gray-400 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500">
                <UsersIcon className="mx-auto h-8 w-8 text-gray-400" />
                <span className="mt-2 block text-sm font-medium text-gray-900">Add Customer</span>
              </button>
              
              <button className="relative block w-full border-2 border-gray-300 border-dashed rounded-lg p-6 text-center hover:border-gray-400 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500">
                <CurrencyDollarIcon className="mx-auto h-8 w-8 text-gray-400" />
                <span className="mt-2 block text-sm font-medium text-gray-900">Record Payment</span>
              </button>
              
              <button className="relative block w-full border-2 border-gray-300 border-dashed rounded-lg p-6 text-center hover:border-gray-400 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500">
                <ChartBarIcon className="mx-auto h-8 w-8 text-gray-400" />
                <span className="mt-2 block text-sm font-medium text-gray-900">View Reports</span>
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
