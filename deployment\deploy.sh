#!/bin/bash

# Global Accounting System Deployment Script
# Usage: ./deploy.sh [environment] [options]

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Default values
ENVIRONMENT="production"
SKIP_TESTS=false
SKIP_BUILD=false
FORCE_DEPLOY=false

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to show usage
show_usage() {
    echo "Usage: $0 [environment] [options]"
    echo ""
    echo "Environments:"
    echo "  production    Deploy to production (default)"
    echo "  staging       Deploy to staging"
    echo "  development   Deploy to development"
    echo ""
    echo "Options:"
    echo "  --skip-tests     Skip running tests"
    echo "  --skip-build     Skip building Docker images"
    echo "  --force          Force deployment without confirmation"
    echo "  --help           Show this help message"
    echo ""
    echo "Examples:"
    echo "  $0 production"
    echo "  $0 staging --skip-tests"
    echo "  $0 development --force"
}

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        production|staging|development)
            ENVIRONMENT="$1"
            shift
            ;;
        --skip-tests)
            SKIP_TESTS=true
            shift
            ;;
        --skip-build)
            SKIP_BUILD=true
            shift
            ;;
        --force)
            FORCE_DEPLOY=true
            shift
            ;;
        --help)
            show_usage
            exit 0
            ;;
        *)
            print_error "Unknown option: $1"
            show_usage
            exit 1
            ;;
    esac
done

print_status "Starting deployment to $ENVIRONMENT environment..."

# Check if required tools are installed
check_requirements() {
    print_status "Checking deployment requirements..."
    
    if ! command -v docker &> /dev/null; then
        print_error "Docker is not installed"
        exit 1
    fi
    
    if ! command -v docker-compose &> /dev/null; then
        print_error "Docker Compose is not installed"
        exit 1
    fi
    
    print_success "All requirements are met"
}

# Run tests
run_tests() {
    if [ "$SKIP_TESTS" = true ]; then
        print_warning "Skipping tests"
        return
    fi
    
    print_status "Running tests..."
    
    # Backend tests
    print_status "Running backend tests..."
    cd backend
    python -m pytest -v --cov=app
    cd ..
    
    # Frontend tests
    print_status "Running frontend tests..."
    cd frontend
    npm test -- --coverage --watchAll=false
    cd ..
    
    print_success "All tests passed"
}

# Build Docker images
build_images() {
    if [ "$SKIP_BUILD" = true ]; then
        print_warning "Skipping Docker build"
        return
    fi
    
    print_status "Building Docker images..."
    
    # Build backend image
    print_status "Building backend image..."
    docker build -t accounting-backend:latest ./backend
    
    # Build frontend image
    print_status "Building frontend image..."
    docker build -t accounting-frontend:latest ./frontend
    
    print_success "Docker images built successfully"
}

# Deploy to environment
deploy() {
    print_status "Deploying to $ENVIRONMENT..."
    
    # Set environment-specific variables
    case $ENVIRONMENT in
        production)
            COMPOSE_FILE="docker-compose.prod.yml"
            ;;
        staging)
            COMPOSE_FILE="docker-compose.staging.yml"
            ;;
        development)
            COMPOSE_FILE="docker-compose.yml"
            ;;
    esac
    
    # Check if compose file exists
    if [ ! -f "$COMPOSE_FILE" ]; then
        print_error "Compose file $COMPOSE_FILE not found"
        exit 1
    fi
    
    # Deploy with Docker Compose
    print_status "Starting services with $COMPOSE_FILE..."
    docker-compose -f "$COMPOSE_FILE" down
    docker-compose -f "$COMPOSE_FILE" up -d
    
    # Wait for services to be ready
    print_status "Waiting for services to be ready..."
    sleep 30
    
    # Health checks
    print_status "Running health checks..."
    
    # Check backend health
    if curl -f http://localhost:8000/health > /dev/null 2>&1; then
        print_success "Backend is healthy"
    else
        print_error "Backend health check failed"
        exit 1
    fi
    
    # Check frontend health
    if curl -f http://localhost:3000 > /dev/null 2>&1; then
        print_success "Frontend is healthy"
    else
        print_error "Frontend health check failed"
        exit 1
    fi
    
    print_success "Deployment completed successfully"
}

# Rollback function
rollback() {
    print_warning "Rolling back deployment..."
    
    # Stop current services
    docker-compose -f "$COMPOSE_FILE" down
    
    # Start previous version (this would need to be implemented based on your versioning strategy)
    print_status "Rollback completed"
}

# Cleanup function
cleanup() {
    print_status "Cleaning up old Docker images..."
    docker image prune -f
    docker container prune -f
    print_success "Cleanup completed"
}

# Main deployment process
main() {
    print_status "Global Accounting System Deployment"
    print_status "Environment: $ENVIRONMENT"
    print_status "Skip tests: $SKIP_TESTS"
    print_status "Skip build: $SKIP_BUILD"
    print_status "Force deploy: $FORCE_DEPLOY"
    echo ""
    
    # Confirmation
    if [ "$FORCE_DEPLOY" = false ]; then
        read -p "Are you sure you want to deploy to $ENVIRONMENT? (y/N): " -n 1 -r
        echo
        if [[ ! $REPLY =~ ^[Yy]$ ]]; then
            print_status "Deployment cancelled"
            exit 0
        fi
    fi
    
    # Run deployment steps
    check_requirements
    run_tests
    build_images
    deploy
    cleanup
    
    print_success "🎉 Deployment to $ENVIRONMENT completed successfully!"
    echo ""
    print_status "Application URLs:"
    case $ENVIRONMENT in
        production)
            echo "  Frontend: https://yourdomain.com"
            echo "  Backend API: https://api.yourdomain.com"
            echo "  API Docs: https://api.yourdomain.com/docs"
            ;;
        staging)
            echo "  Frontend: https://staging.yourdomain.com"
            echo "  Backend API: https://api-staging.yourdomain.com"
            echo "  API Docs: https://api-staging.yourdomain.com/docs"
            ;;
        development)
            echo "  Frontend: http://localhost:3000"
            echo "  Backend API: http://localhost:8000"
            echo "  API Docs: http://localhost:8000/docs"
            ;;
    esac
}

# Handle script interruption
trap 'print_error "Deployment interrupted"; exit 1' INT TERM

# Run main function
main
