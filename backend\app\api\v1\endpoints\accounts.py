"""
Accounts endpoints.
"""

from typing import List, Optional
from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.ext.asyncio import AsyncSession

from app.core.database import get_db
from app.core.security import get_current_active_user, require_permissions
from app.models.user import User
from app.models.account import AccountType
from app.services.account_service import AccountService
from app.schemas.account import (
    AccountResponse, AccountCreate, AccountUpdate, AccountList,
    ChartOfAccountsResponse, TrialBalanceResponse, TrialBalanceItem
)

router = APIRouter()


@router.get("/", response_model=AccountList)
async def get_accounts(
    skip: int = Query(0, ge=0),
    limit: int = Query(20, ge=1, le=100),
    search: Optional[str] = Query(None),
    account_type: Optional[AccountType] = Query(None),
    active_only: bool = Query(False),
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """Get list of accounts with pagination and filtering."""
    account_service = AccountService(db)
    
    # Build filters
    filters = {"company_id": current_user.company_id}
    if account_type:
        filters["account_type"] = account_type
    if active_only:
        filters["is_active"] = True
    
    # Get accounts
    if search:
        accounts = await account_service.search_accounts(search, current_user.company_id)
        total = len(accounts)
        accounts = accounts[skip:skip + limit]
    else:
        accounts = await account_service.get_multi(skip=skip, limit=limit, filters=filters)
        total = await account_service.count(filters)
    
    return AccountList(
        accounts=accounts,
        total=total,
        page=skip // limit + 1,
        size=limit,
        pages=(total + limit - 1) // limit
    )


@router.get("/chart", response_model=ChartOfAccountsResponse)
async def get_chart_of_accounts(
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """Get organized chart of accounts."""
    account_service = AccountService(db)
    chart = await account_service.get_chart_of_accounts(current_user.company_id)
    
    return ChartOfAccountsResponse(
        assets=chart["assets"],
        liabilities=chart["liabilities"],
        equity=chart["equity"],
        revenue=chart["revenue"],
        expenses=chart["expenses"]
    )


@router.get("/hierarchy")
async def get_hierarchical_accounts(
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """Get accounts in hierarchical structure."""
    account_service = AccountService(db)
    hierarchy = await account_service.get_hierarchical_accounts(current_user.company_id)
    return {"accounts": hierarchy}


@router.get("/trial-balance", response_model=TrialBalanceResponse)
async def get_trial_balance(
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """Get trial balance."""
    account_service = AccountService(db)
    trial_balance = await account_service.get_trial_balance(current_user.company_id)
    
    items = [TrialBalanceItem(**item) for item in trial_balance]
    total_debits = sum(item.debit_balance for item in items if item.account_id is not None)
    total_credits = sum(item.credit_balance for item in items if item.account_id is not None)
    
    return TrialBalanceResponse(
        items=items,
        total_debits=total_debits,
        total_credits=total_credits,
        is_balanced=total_debits == total_credits
    )


@router.get("/{account_id}", response_model=AccountResponse)
async def get_account(
    account_id: int,
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """Get account by ID."""
    account_service = AccountService(db)
    account = await account_service.get_by_id(account_id)
    
    if not account:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Account not found"
        )
    
    # Check if account belongs to user's company
    if account.company_id != current_user.company_id:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Not enough permissions"
        )
    
    return account


@router.post("/", response_model=AccountResponse)
@require_permissions("manage_accounts")
async def create_account(
    account_data: AccountCreate,
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """Create a new account."""
    account_service = AccountService(db)
    
    # Set company_id
    account_data.company_id = current_user.company_id
    
    # Check if account code already exists
    existing_account = await account_service.get_by_code(account_data.code, current_user.company_id)
    if existing_account:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Account code already exists"
        )
    
    # Create account
    account = await account_service.create(account_data.dict())
    return account


@router.put("/{account_id}", response_model=AccountResponse)
@require_permissions("manage_accounts")
async def update_account(
    account_id: int,
    account_data: AccountUpdate,
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """Update account."""
    account_service = AccountService(db)
    account = await account_service.get_by_id(account_id)
    
    if not account:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Account not found"
        )
    
    # Check if account belongs to user's company
    if account.company_id != current_user.company_id:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Not enough permissions"
        )
    
    # Check if it's a system account
    if account.is_system:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Cannot modify system account"
        )
    
    # Update account
    updated_account = await account_service.update(account_id, account_data.dict(exclude_unset=True))
    return updated_account


@router.delete("/{account_id}")
@require_permissions("manage_accounts")
async def delete_account(
    account_id: int,
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """Delete account."""
    account_service = AccountService(db)
    account = await account_service.get_by_id(account_id)
    
    if not account:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Account not found"
        )
    
    # Check if account belongs to user's company
    if account.company_id != current_user.company_id:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Not enough permissions"
        )
    
    # Check if account can be deleted
    if not account.can_be_deleted():
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Account cannot be deleted. It may be a system account, have a balance, or have child accounts."
        )
    
    success = await account_service.delete(account_id)
    if not success:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Failed to delete account"
        )
    
    return {"message": "Account deleted successfully"}


@router.get("/type/{account_type}", response_model=List[AccountResponse])
async def get_accounts_by_type(
    account_type: AccountType,
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """Get accounts by type."""
    account_service = AccountService(db)
    accounts = await account_service.get_by_type(account_type, current_user.company_id)
    return accounts


@router.get("/active/list", response_model=List[AccountResponse])
async def get_active_accounts(
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """Get only active accounts."""
    account_service = AccountService(db)
    accounts = await account_service.get_active_accounts(current_user.company_id)
    return accounts
