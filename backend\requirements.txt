# FastAPI and ASGI server
fastapi==0.104.1
uvicorn[standard]==0.24.0

# Database
sqlalchemy==2.0.23
alembic==1.12.1
psycopg2-binary==2.9.9

# Supabase
supabase==2.0.2
postgrest==0.13.0

# Authentication and Security
python-jose[cryptography]==3.3.0
passlib[bcrypt]==1.7.4
python-multipart==0.0.6

# Data validation and serialization
pydantic==2.5.0
pydantic-settings==2.1.0

# HTTP client
httpx==0.25.2
requests==2.31.0

# Date and time handling
python-dateutil==2.8.2

# Excel and PDF generation
openpyxl==3.1.2
reportlab==4.0.7
fpdf2==2.7.6

# Email
fastapi-mail==1.4.1

# Background tasks
celery==5.3.4
redis==5.0.1

# Logging and monitoring
structlog==23.2.0

# Environment configuration
python-dotenv==1.0.0

# Database async support
asyncpg==0.29.0

# Testing
pytest==7.4.3
pytest-asyncio==0.21.1
httpx==0.25.2

# Development tools
black==23.11.0
flake8==6.1.0
mypy==1.7.1

# Production server
gunicorn==21.2.0
pre-commit==3.6.0

# CORS
fastapi-cors==0.0.6

# Environment variables
python-dotenv==1.0.0

# Currency and localization
babel==2.13.1
forex-python==1.8

# Math and calculations
decimal==1.70

# File handling
aiofiles==23.2.1

# Caching
fastapi-cache2==0.2.1

# Rate limiting
slowapi==0.1.9

# API documentation
fastapi-users==12.1.2

# Utilities
python-slugify==8.0.1
