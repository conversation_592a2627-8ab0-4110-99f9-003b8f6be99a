# Global Accounting Web Application

A comprehensive, production-ready accounting system similar to Daftra, built with modern web technologies and designed for scalability, security, and ease of use.

## 🌟 Features

### Core Accounting
- ✅ **Chart of Accounts** - Hierarchical account structure with customizable categories
- ✅ **Double-Entry Bookkeeping** - Automated journal entries ensuring balanced transactions
- ✅ **General Ledger** - Complete transaction history with detailed audit trails
- ✅ **Financial Reports** - Profit & Loss, Balance Sheet, Cash Flow, Trial Balance
- ✅ **Multi-Currency Support** - Handle international transactions with real-time exchange rates
- ✅ **Tax Management** - Automated tax calculations and compliance reporting

### Business Management
- ✅ **Customer & Supplier CRM** - Complete contact management with transaction history
- ✅ **Invoice Management** - Professional invoice generation with customizable templates
- ✅ **Inventory Tracking** - Real-time stock management with automated alerts
- ✅ **Payment Processing** - Accounts receivable/payable with aging reports
- ✅ **Multi-Company Support** - Manage multiple businesses from one dashboard

### Advanced Features
- ✅ **Real-Time Dashboard** - Live analytics and KPIs with interactive charts
- ✅ **Role-Based Access Control** - Granular permissions for different user types
- ✅ **Export Capabilities** - PDF, Excel, and CSV exports for all reports
- ✅ **RESTful API** - Complete API for third-party integrations
- ✅ **Mobile-Responsive Design** - Works seamlessly on all devices
- ✅ **Automated Backups** - Secure data protection and recovery

## 🛠 Technology Stack

### Backend
- **FastAPI** - High-performance Python web framework with automatic API documentation
- **PostgreSQL** - Robust relational database with ACID compliance
- **SQLAlchemy** - Modern Python ORM with async support
- **Pydantic** - Data validation and serialization with type hints
- **Redis** - In-memory caching and session storage
- **Celery** - Distributed task queue for background jobs

### Frontend
- **Next.js 14** - React framework with App Router and Server Components
- **TypeScript** - Type-safe JavaScript for better development experience
- **Tailwind CSS** - Utility-first CSS framework for rapid UI development
- **React Query** - Powerful data fetching and state management
- **Chart.js** - Beautiful, responsive charts and data visualization
- **React Hook Form** - Performant forms with easy validation

### Database & Storage
- **PostgreSQL 15** - Primary database with advanced features
- **Redis 7** - Caching, session storage, and real-time features
- **Supabase** - Optional backend-as-a-service for rapid development

### DevOps & Deployment
- **Docker** - Containerization for consistent environments
- **Nginx** - Reverse proxy and load balancer
- **GitHub Actions** - Automated CI/CD pipeline
- **Vercel/Railway** - Cloud deployment platforms

## Project Structure

```
accounting-app/
├── backend/                 # FastAPI backend
│   ├── app/
│   │   ├── api/            # API routes
│   │   ├── core/           # Core configurations
│   │   ├── models/         # Database models
│   │   ├── services/       # Business logic
│   │   └── utils/          # Utility functions
│   ├── tests/              # Backend tests
│   └── requirements.txt
├── frontend/               # Next.js frontend
│   ├── components/         # React components
│   ├── pages/             # Next.js pages
│   ├── hooks/             # Custom hooks
│   ├── utils/             # Utility functions
│   └── styles/            # CSS/Tailwind styles
├── database/              # Database schemas and migrations
├── docs/                  # Documentation
└── deployment/           # Deployment configurations
```

## 🚀 Quick Start

### Prerequisites
- **Python 3.9+** - Backend development
- **Node.js 18+** - Frontend development
- **PostgreSQL 12+** - Database (or use Docker)
- **Redis** - Caching (optional, or use Docker)
- **Git** - Version control

### Automated Setup (Recommended)

```bash
# Clone the repository
git clone https://github.com/yourusername/global-accounting-system.git
cd global-accounting-system

# Run the setup script
chmod +x setup.sh
./setup.sh
```

The setup script will:
- Check system requirements
- Set up Python virtual environment
- Install all dependencies
- Create environment files
- Set up the database
- Create an initial admin user

### Manual Setup

#### Backend Setup
```bash
cd backend

# Create virtual environment
python -m venv venv
source venv/bin/activate  # Windows: venv\Scripts\activate

# Install dependencies
pip install -r requirements.txt

# Copy environment file
cp .env.example .env
# Edit .env with your configuration

# Run database migrations
alembic upgrade head

# Start the server
uvicorn app.main:app --reload
```

#### Frontend Setup
```bash
cd frontend

# Install dependencies
npm install

# Copy environment file
cp .env.example .env.local
# Edit .env.local with your configuration

# Start development server
npm run dev
```

### Docker Setup (Alternative)

```bash
# Start all services with Docker
docker-compose up -d

# View logs
docker-compose logs -f

# Stop services
docker-compose down
```

## 🔧 Development

### Project Structure
```
accounting-app/
├── backend/                 # FastAPI backend
│   ├── app/
│   │   ├── api/            # API routes and endpoints
│   │   ├── core/           # Core configurations and utilities
│   │   ├── models/         # SQLAlchemy database models
│   │   ├── services/       # Business logic layer
│   │   ├── schemas/        # Pydantic schemas for validation
│   │   └── utils/          # Utility functions
│   ├── tests/              # Backend tests
│   └── requirements.txt    # Python dependencies
├── frontend/               # Next.js frontend
│   ├── app/               # Next.js 14 App Router
│   ├── components/        # Reusable React components
│   ├── hooks/             # Custom React hooks
│   ├── lib/               # Utility libraries and API client
│   └── styles/            # CSS and styling
├── deployment/            # Deployment configurations
├── .github/workflows/     # CI/CD pipelines
└── docker-compose.yml    # Docker services
```

### Database Migrations
```bash
cd backend
source venv/bin/activate

# Create new migration
alembic revision --autogenerate -m "Add new feature"

# Apply migrations
alembic upgrade head

# Rollback migration
alembic downgrade -1
```

### Testing
```bash
# Backend tests with coverage
cd backend
pytest -v --cov=app --cov-report=html

# Frontend tests
cd frontend
npm test -- --coverage

# End-to-end tests
npm run test:e2e
```

### Code Quality & Linting
```bash
# Backend code quality
cd backend
black app/                 # Code formatting
flake8 app/               # Linting
mypy app/                 # Type checking
pytest --cov=app         # Test coverage

# Frontend code quality
cd frontend
npm run lint              # ESLint
npm run type-check        # TypeScript checking
npm run format            # Prettier formatting
```

### API Documentation
- **Interactive API Docs**: http://localhost:8000/docs (Swagger UI)
- **Alternative Docs**: http://localhost:8000/redoc (ReDoc)
- **OpenAPI Schema**: http://localhost:8000/openapi.json

## 🚀 Deployment

### Production Deployment Options

#### Option 1: Docker Deployment (Recommended)
```bash
# Build and deploy with Docker
docker-compose -f docker-compose.prod.yml up -d

# Scale services
docker-compose -f docker-compose.prod.yml up -d --scale backend=3
```

#### Option 2: Cloud Deployment
- **Frontend**: Vercel, Netlify, or AWS Amplify
- **Backend**: Railway, Render, or AWS ECS
- **Database**: Supabase, AWS RDS, or Google Cloud SQL

#### Option 3: VPS Deployment
```bash
# Deploy to your VPS
./deployment/deploy.sh production
```

### Environment Configuration

#### Production Environment Variables
```bash
# Backend (.env)
DATABASE_URL=********************************/db
REDIS_URL=redis://host:6379
SECRET_KEY=your-super-secret-production-key
DEBUG=False
ALLOWED_HOSTS=["https://yourdomain.com"]

# Frontend (.env.local)
NEXT_PUBLIC_API_URL=https://api.yourdomain.com
NODE_ENV=production
```

### CI/CD Pipeline
The application includes a complete CI/CD pipeline with GitHub Actions:
- ✅ Automated testing for backend and frontend
- ✅ Code quality checks and security scanning
- ✅ Docker image building and pushing
- ✅ Automated deployment to production
- ✅ Health checks and rollback capabilities

## 📊 Features Overview

### Dashboard & Analytics
- **Real-time KPIs** - Revenue, expenses, profit margins, and cash flow
- **Interactive Charts** - Revenue trends, expense analysis, and financial forecasts
- **Quick Actions** - One-click access to common tasks
- **Recent Activity** - Live feed of system activities and transactions

### Accounting Features
- **Chart of Accounts** - Customizable account hierarchy with industry templates
- **Journal Entries** - Manual and automated double-entry bookkeeping
- **Financial Reports** - P&L, Balance Sheet, Cash Flow, Trial Balance
- **Multi-Currency** - Support for 150+ currencies with real-time rates
- **Tax Management** - Automated calculations and compliance reporting

### Business Management
- **Customer Management** - Complete CRM with contact history and credit limits
- **Supplier Management** - Vendor tracking with payment terms and history
- **Invoice Management** - Professional invoices with customizable templates
- **Inventory Control** - Stock tracking, alerts, and valuation methods
- **Payment Processing** - A/R and A/P management with aging reports

### Security & Compliance
- **Role-Based Access** - Granular permissions for different user types
- **Audit Trails** - Complete transaction history and user activity logs
- **Data Encryption** - End-to-end encryption for sensitive financial data
- **Backup & Recovery** - Automated backups with point-in-time recovery
- **Compliance Ready** - GAAP, IFRS, and local accounting standards support

## 🔐 Security

### Authentication & Authorization
- **JWT-based Authentication** - Secure token-based authentication
- **Role-Based Access Control** - Fine-grained permissions system
- **Multi-Factor Authentication** - Optional 2FA for enhanced security
- **Session Management** - Secure session handling with automatic timeout

### Data Protection
- **Encryption at Rest** - Database encryption for sensitive data
- **Encryption in Transit** - TLS/SSL for all communications
- **Input Validation** - Comprehensive data validation and sanitization
- **SQL Injection Protection** - Parameterized queries and ORM protection

### Compliance & Auditing
- **Audit Logs** - Comprehensive logging of all user actions
- **Data Retention** - Configurable data retention policies
- **GDPR Compliance** - Data privacy and user rights management
- **SOX Compliance** - Financial reporting controls and documentation

## 🤝 Contributing

We welcome contributions from the community! Here's how you can help:

### Getting Started
1. **Fork the repository** on GitHub
2. **Clone your fork** locally
3. **Create a feature branch** from `develop`
4. **Make your changes** with proper tests
5. **Submit a pull request** with a clear description

### Development Guidelines
- Follow the existing code style and conventions
- Write comprehensive tests for new features
- Update documentation for any API changes
- Ensure all tests pass before submitting PR
- Use conventional commit messages

### Code Style
- **Backend**: Follow PEP 8 with Black formatting
- **Frontend**: Use ESLint and Prettier configurations
- **Commits**: Use conventional commit format
- **Documentation**: Update relevant docs with changes

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🆘 Support

### Documentation
- **API Documentation**: Available at `/docs` when running the backend
- **User Guide**: Comprehensive user documentation in `/docs` folder
- **Developer Guide**: Technical documentation for contributors

### Getting Help
- **GitHub Issues**: Report bugs and request features
- **Discussions**: Community discussions and Q&A
- **Email Support**: <EMAIL>

### Professional Support
For enterprise customers, we offer:
- Priority support and bug fixes
- Custom feature development
- Training and onboarding
- Dedicated account management

## 🙏 Acknowledgments

- **FastAPI** - For the excellent Python web framework
- **Next.js** - For the powerful React framework
- **Tailwind CSS** - For the utility-first CSS framework
- **PostgreSQL** - For the robust database system
- **All Contributors** - Thank you for making this project better!

---

**Built with ❤️ for the global business community**

*Transform your business with modern, secure, and scalable accounting software.*
