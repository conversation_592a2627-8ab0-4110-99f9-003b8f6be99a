# Global Accounting Web Application

A comprehensive accounting system similar to Daftra with modern web technologies.

## Features

### Core Accounting
- ✅ Chart of Accounts with hierarchical structure
- ✅ Double-entry bookkeeping system
- ✅ Journal entries and general ledger
- ✅ Financial reports (P&L, Balance Sheet, Cash Flow)
- ✅ Multi-currency support
- ✅ Tax calculation and compliance

### Business Management
- ✅ Customer and supplier management (CRM)
- ✅ Invoice generation (sales and purchase)
- ✅ Inventory management with stock tracking
- ✅ Payment tracking and A/R, A/P management
- ✅ Multi-company/branch support

### Advanced Features
- ✅ Real-time notifications and updates
- ✅ Dashboard with analytics and KPIs
- ✅ Export capabilities (PDF, Excel)
- ✅ API for third-party integrations
- ✅ Mobile-responsive design
- ✅ User roles and permissions

## Technology Stack

### Backend
- **FastAPI** - High-performance Python web framework
- **Supabase** - Database, authentication, and real-time features
- **Pydantic** - Data validation and serialization
- **SQLAlchemy** - ORM for database operations
- **Alembic** - Database migrations

### Frontend
- **Next.js** - React framework with SSR/SSG
- **TypeScript** - Type-safe JavaScript
- **Tailwind CSS** - Utility-first CSS framework
- **React Query** - Data fetching and state management
- **Chart.js** - Data visualization
- **React Hook Form** - Form handling

### Database
- **PostgreSQL** (via Supabase) - Primary database
- **Redis** - Caching and session storage

### Deployment
- **Vercel** - Frontend deployment
- **Railway/Render** - Backend deployment
- **GitHub Actions** - CI/CD pipeline

## Project Structure

```
accounting-app/
├── backend/                 # FastAPI backend
│   ├── app/
│   │   ├── api/            # API routes
│   │   ├── core/           # Core configurations
│   │   ├── models/         # Database models
│   │   ├── services/       # Business logic
│   │   └── utils/          # Utility functions
│   ├── tests/              # Backend tests
│   └── requirements.txt
├── frontend/               # Next.js frontend
│   ├── components/         # React components
│   ├── pages/             # Next.js pages
│   ├── hooks/             # Custom hooks
│   ├── utils/             # Utility functions
│   └── styles/            # CSS/Tailwind styles
├── database/              # Database schemas and migrations
├── docs/                  # Documentation
└── deployment/           # Deployment configurations
```

## Quick Start

### Prerequisites
- Python 3.9+
- Node.js 18+
- Supabase account

### Backend Setup
```bash
cd backend
python -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate
pip install -r requirements.txt
uvicorn app.main:app --reload
```

### Frontend Setup
```bash
cd frontend
npm install
npm run dev
```

### Environment Variables
Create `.env` files in both backend and frontend directories with the required environment variables (see `.env.example` files).

## Development

### Database Migrations
```bash
cd backend
alembic revision --autogenerate -m "Description"
alembic upgrade head
```

### Testing
```bash
# Backend tests
cd backend
pytest

# Frontend tests
cd frontend
npm test
```

### Code Quality
```bash
# Backend
black app/
flake8 app/
mypy app/

# Frontend
npm run lint
npm run type-check
```

## Deployment

The application is configured for automatic deployment:
- Frontend: Deployed to Vercel on push to main branch
- Backend: Deployed to Railway/Render on push to main branch
- Database: Managed by Supabase

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests
5. Submit a pull request

## License

MIT License - see LICENSE file for details.

## Support

For support and questions, please open an issue in the GitHub repository.
