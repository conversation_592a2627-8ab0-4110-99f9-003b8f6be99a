<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>نظام المحاسبة العالمي - Global Accounting System</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://unpkg.com/axios/dist/axios.min.js"></script>
    <style>
        body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; }
        .rtl { direction: rtl; text-align: right; }
        .ltr { direction: ltr; text-align: left; }
    </style>
</head>
<body class="bg-gray-50 rtl">
    <div class="min-h-screen">
        <!-- Header -->
        <header class="bg-blue-600 text-white shadow-lg">
            <div class="container mx-auto px-4 py-6">
                <div class="flex justify-between items-center">
                    <div>
                        <h1 class="text-3xl font-bold">نظام المحاسبة العالمي</h1>
                        <p class="text-blue-100">Global Accounting System</p>
                    </div>
                    <div class="flex space-x-4 space-x-reverse">
                        <button id="loginBtn" class="bg-white text-blue-600 px-4 py-2 rounded-lg hover:bg-gray-100">
                            تسجيل الدخول
                        </button>
                        <button id="logoutBtn" class="bg-red-500 text-white px-4 py-2 rounded-lg hover:bg-red-600 hidden">
                            تسجيل الخروج
                        </button>
                    </div>
                </div>
            </div>
        </header>

        <!-- Main Content -->
        <main class="container mx-auto px-4 py-8">
            <!-- Welcome Section -->
            <div id="welcomeSection" class="bg-white rounded-lg shadow-md p-6 mb-8">
                <h2 class="text-2xl font-bold text-gray-800 mb-4">مرحباً بك في نظام المحاسبة العالمي</h2>
                <p class="text-gray-600 mb-4">
                    نظام محاسبة شامل ومتطور يوفر جميع الأدوات اللازمة لإدارة أعمالك المحاسبية بكفاءة عالية
                </p>
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                    <div class="bg-blue-50 p-4 rounded-lg text-center">
                        <div class="text-2xl font-bold text-blue-600">📊</div>
                        <h3 class="font-semibold text-gray-800">دليل الحسابات</h3>
                        <p class="text-sm text-gray-600">إدارة شاملة للحسابات</p>
                    </div>
                    <div class="bg-green-50 p-4 rounded-lg text-center">
                        <div class="text-2xl font-bold text-green-600">👥</div>
                        <h3 class="font-semibold text-gray-800">إدارة العملاء</h3>
                        <p class="text-sm text-gray-600">نظام CRM متكامل</p>
                    </div>
                    <div class="bg-yellow-50 p-4 rounded-lg text-center">
                        <div class="text-2xl font-bold text-yellow-600">📄</div>
                        <h3 class="font-semibold text-gray-800">الفواتير</h3>
                        <p class="text-sm text-gray-600">إنشاء وإدارة الفواتير</p>
                    </div>
                    <div class="bg-purple-50 p-4 rounded-lg text-center">
                        <div class="text-2xl font-bold text-purple-600">📈</div>
                        <h3 class="font-semibold text-gray-800">التقارير</h3>
                        <p class="text-sm text-gray-600">تقارير مالية شاملة</p>
                    </div>
                </div>
            </div>

            <!-- Login Form -->
            <div id="loginForm" class="bg-white rounded-lg shadow-md p-6 mb-8 hidden">
                <h3 class="text-xl font-bold text-gray-800 mb-4">تسجيل الدخول</h3>
                <form id="loginFormElement">
                    <div class="mb-4">
                        <label class="block text-gray-700 text-sm font-bold mb-2">
                            اسم المستخدم أو البريد الإلكتروني
                        </label>
                        <input type="text" id="username" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:border-blue-500" required>
                    </div>
                    <div class="mb-6">
                        <label class="block text-gray-700 text-sm font-bold mb-2">
                            كلمة المرور
                        </label>
                        <input type="password" id="password" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:border-blue-500" required>
                    </div>
                    <button type="submit" class="w-full bg-blue-600 text-white py-2 px-4 rounded-lg hover:bg-blue-700">
                        تسجيل الدخول
                    </button>
                </form>
                <div class="mt-4 p-4 bg-gray-100 rounded-lg">
                    <h4 class="font-semibold text-gray-800">بيانات تجريبية:</h4>
                    <p class="text-sm text-gray-600">اسم المستخدم: admin</p>
                    <p class="text-sm text-gray-600">كلمة المرور: admin123</p>
                </div>
            </div>

            <!-- Dashboard -->
            <div id="dashboard" class="hidden">
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
                    <div class="bg-white rounded-lg shadow-md p-6">
                        <h3 class="text-lg font-semibold text-gray-800 mb-2">العملاء</h3>
                        <p class="text-3xl font-bold text-blue-600" id="customersCount">-</p>
                    </div>
                    <div class="bg-white rounded-lg shadow-md p-6">
                        <h3 class="text-lg font-semibold text-gray-800 mb-2">الموردين</h3>
                        <p class="text-3xl font-bold text-green-600" id="suppliersCount">-</p>
                    </div>
                    <div class="bg-white rounded-lg shadow-md p-6">
                        <h3 class="text-lg font-semibold text-gray-800 mb-2">المنتجات</h3>
                        <p class="text-3xl font-bold text-yellow-600" id="productsCount">-</p>
                    </div>
                    <div class="bg-white rounded-lg shadow-md p-6">
                        <h3 class="text-lg font-semibold text-gray-800 mb-2">الفواتير</h3>
                        <p class="text-3xl font-bold text-purple-600" id="invoicesCount">-</p>
                    </div>
                </div>

                <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                    <div class="bg-white rounded-lg shadow-md p-6">
                        <h3 class="text-lg font-semibold text-gray-800 mb-4">الشركات</h3>
                        <div id="companiesList" class="space-y-2">
                            <!-- Companies will be loaded here -->
                        </div>
                    </div>
                    <div class="bg-white rounded-lg shadow-md p-6">
                        <h3 class="text-lg font-semibold text-gray-800 mb-4">الحسابات</h3>
                        <div id="accountsList" class="space-y-2 max-h-64 overflow-y-auto">
                            <!-- Accounts will be loaded here -->
                        </div>
                    </div>
                </div>
            </div>

            <!-- API Documentation Link -->
            <div class="bg-white rounded-lg shadow-md p-6 mt-8">
                <h3 class="text-lg font-semibold text-gray-800 mb-4">وثائق API</h3>
                <p class="text-gray-600 mb-4">يمكنك الوصول إلى وثائق API التفاعلية للنظام</p>
                <a href="/docs" target="_blank" class="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 inline-block">
                    عرض وثائق API
                </a>
            </div>
        </main>
    </div>

    <script>
        const API_BASE = window.location.origin;
        let authToken = localStorage.getItem('authToken');

        // Elements
        const loginBtn = document.getElementById('loginBtn');
        const logoutBtn = document.getElementById('logoutBtn');
        const loginForm = document.getElementById('loginForm');
        const dashboard = document.getElementById('dashboard');
        const loginFormElement = document.getElementById('loginFormElement');

        // Initialize
        if (authToken) {
            showDashboard();
        }

        // Event Listeners
        loginBtn.addEventListener('click', () => {
            loginForm.classList.toggle('hidden');
        });

        logoutBtn.addEventListener('click', () => {
            localStorage.removeItem('authToken');
            authToken = null;
            showLogin();
        });

        loginFormElement.addEventListener('submit', async (e) => {
            e.preventDefault();
            const username = document.getElementById('username').value;
            const password = document.getElementById('password').value;

            try {
                const response = await axios.post(`${API_BASE}/api/v1/auth/login`, {
                    username,
                    password
                });

                authToken = response.data.access_token;
                localStorage.setItem('authToken', authToken);
                showDashboard();
            } catch (error) {
                alert('خطأ في تسجيل الدخول: ' + (error.response?.data?.detail || error.message));
            }
        });

        function showLogin() {
            loginBtn.classList.remove('hidden');
            logoutBtn.classList.add('hidden');
            loginForm.classList.remove('hidden');
            dashboard.classList.add('hidden');
        }

        function showDashboard() {
            loginBtn.classList.add('hidden');
            logoutBtn.classList.remove('hidden');
            loginForm.classList.add('hidden');
            dashboard.classList.remove('hidden');
            loadDashboardData();
        }

        async function loadDashboardData() {
            try {
                // Load stats
                const statsResponse = await axios.get(`${API_BASE}/api/v1/dashboard/stats`, {
                    headers: { Authorization: `Bearer ${authToken}` }
                });
                
                document.getElementById('customersCount').textContent = statsResponse.data.customers_count;
                document.getElementById('suppliersCount').textContent = statsResponse.data.suppliers_count;
                document.getElementById('productsCount').textContent = statsResponse.data.products_count;
                document.getElementById('invoicesCount').textContent = statsResponse.data.invoices_count;

                // Load companies
                const companiesResponse = await axios.get(`${API_BASE}/api/v1/companies`, {
                    headers: { Authorization: `Bearer ${authToken}` }
                });
                
                const companiesList = document.getElementById('companiesList');
                companiesList.innerHTML = companiesResponse.data.map(company => 
                    `<div class="p-2 bg-gray-50 rounded">
                        <strong>${company.name}</strong>
                        ${company.email ? `<br><small class="text-gray-600">${company.email}</small>` : ''}
                    </div>`
                ).join('');

                // Load accounts
                const accountsResponse = await axios.get(`${API_BASE}/api/v1/accounts`, {
                    headers: { Authorization: `Bearer ${authToken}` }
                });
                
                const accountsList = document.getElementById('accountsList');
                accountsList.innerHTML = accountsResponse.data.map(account => 
                    `<div class="p-2 bg-gray-50 rounded flex justify-between">
                        <span><strong>${account.code}</strong> - ${account.name}</span>
                        <span class="text-sm text-gray-600">${account.current_balance} ر.س</span>
                    </div>`
                ).join('');

            } catch (error) {
                console.error('Error loading dashboard data:', error);
                if (error.response?.status === 401) {
                    localStorage.removeItem('authToken');
                    showLogin();
                }
            }
        }
    </script>
</body>
</html>
