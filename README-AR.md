# نظام المحاسبة العالمي 🌍

نظام محاسبة شامل ومتطور مبني بأحدث التقنيات، يوفر جميع الميزات المطلوبة لإدارة الأعمال المحاسبية بكفاءة عالية.

## 🌟 الميزات الرئيسية

### 📊 النظام المحاسبي الأساسي
- ✅ **دليل الحسابات** - هيكل حسابات هرمي قابل للتخصيص
- ✅ **القيد المزدوج** - نظام محاسبي متوازن تلقائياً
- ✅ **دفتر الأستاذ العام** - سجل كامل للمعاملات مع مسارات التدقيق
- ✅ **التقارير المالية** - قائمة الدخل، الميزانية العمومية، التدفق النقدي
- ✅ **العملات المتعددة** - دعم المعاملات الدولية
- ✅ **إدارة الضرائب** - حساب الضرائب التلقائي والامتثال

### 💼 إدارة الأعمال
- ✅ **إدارة العملاء** - نظام CRM متكامل مع تاريخ المعاملات
- ✅ **إدارة الموردين** - متابعة الموردين مع شروط الدفع
- ✅ **إدارة الفواتير** - إنشاء فواتير احترافية مع قوالب قابلة للتخصيص
- ✅ **تتبع المخزون** - إدارة المخزون في الوقت الفعلي مع التنبيهات
- ✅ **معالجة المدفوعات** - حسابات القبض والدفع مع تقارير الأعمار
- ✅ **دعم الشركات المتعددة** - إدارة عدة أعمال من لوحة تحكم واحدة

### 🚀 الميزات المتقدمة
- ✅ **لوحة تحكم فورية** - تحليلات ومؤشرات أداء تفاعلية
- ✅ **التحكم في الصلاحيات** - صلاحيات مفصلة لأنواع المستخدمين المختلفة
- ✅ **إمكانيات التصدير** - تصدير PDF وExcel وCSV لجميع التقارير
- ✅ **تصميم متجاوب** - يعمل بسلاسة على جميع الأجهزة
- ✅ **النسخ الاحتياطية التلقائية** - حماية البيانات والاسترداد الآمن
- ✅ **تكامل API** - واجهة برمجة تطبيقات كاملة للتكاملات الخارجية

## 🛠 التقنيات المستخدمة

### الخادم الخلفي
- **FastAPI** - إطار عمل Python عالي الأداء مع توثيق API تلقائي
- **PostgreSQL** - قاعدة بيانات علائقية قوية مع امتثال ACID
- **SQLAlchemy** - ORM حديث لـ Python مع دعم async
- **Pydantic** - التحقق من البيانات والتسلسل مع تلميحات النوع
- **Redis** - تخزين مؤقت في الذاكرة وإدارة الجلسات

### الواجهة الأمامية
- **Next.js 14** - إطار عمل React مع App Router ومكونات الخادم
- **TypeScript** - JavaScript آمن النوع لتجربة تطوير أفضل
- **Tailwind CSS** - إطار عمل CSS utility-first للتطوير السريع للواجهة
- **React Query** - جلب البيانات وإدارة الحالة القوية
- **Chart.js** - مخططات جميلة ومتجاوبة وتصور البيانات

### قاعدة البيانات والتخزين
- **PostgreSQL 15** - قاعدة البيانات الأساسية مع ميزات متقدمة
- **Redis 7** - التخزين المؤقت وتخزين الجلسات والميزات الفورية
- **Supabase** - خدمة backend-as-a-service للتطوير السريع

## 🚀 البدء السريع

### المتطلبات الأساسية
- **Python 3.9+** - تطوير الخادم الخلفي
- **Node.js 18+** - تطوير الواجهة الأمامية
- **PostgreSQL 12+** - قاعدة البيانات (أو استخدم Docker)
- **Git** - التحكم في الإصدار

### الإعداد التلقائي (موصى به)

```bash
# استنساخ المستودع
git clone https://github.com/yourusername/global-accounting-system.git
cd global-accounting-system

# تشغيل سكريبت الإعداد
chmod +x setup.sh
./setup.sh
```

### الإعداد اليدوي

#### إعداد الخادم الخلفي
```bash
cd backend

# إنشاء بيئة افتراضية
python -m venv venv
source venv/bin/activate  # Windows: venv\Scripts\activate

# تثبيت التبعيات
pip install -r requirements.txt

# نسخ ملف البيئة
cp .env.example .env
# تحرير .env مع التكوين الخاص بك

# تشغيل ترحيل قاعدة البيانات
alembic upgrade head

# بدء الخادم
uvicorn app.main:app --reload
```

#### إعداد الواجهة الأمامية
```bash
cd frontend

# تثبيت التبعيات
npm install

# نسخ ملف البيئة
cp .env.example .env.local
# تحرير .env.local مع التكوين الخاص بك

# بدء خادم التطوير
npm run dev
```

## 🌐 النشر المباشر

### معلومات الوصول
- **الموقع الإلكتروني**: https://global-accounting-frontend.vercel.app
- **واجهة برمجة التطبيقات**: https://global-accounting-backend.railway.app
- **وثائق API**: https://global-accounting-backend.railway.app/docs

### بيانات تسجيل الدخول
- **البريد الإلكتروني**: <EMAIL>
- **كلمة المرور**: admin123

## 📊 نظرة عامة على الميزات

### لوحة التحكم والتحليلات
- **مؤشرات الأداء الفورية** - الإيرادات والمصروفات وهوامش الربح والتدفق النقدي
- **المخططات التفاعلية** - اتجاهات الإيرادات وتحليل المصروفات والتوقعات المالية
- **الإجراءات السريعة** - وصول بنقرة واحدة للمهام الشائعة
- **النشاط الأخير** - تغذية مباشرة لأنشطة النظام والمعاملات

### الميزات المحاسبية
- **دليل الحسابات** - تسلسل هرمي قابل للتخصيص مع قوالب الصناعة
- **قيود اليومية** - مسك الدفاتر بالقيد المزدوج اليدوي والتلقائي
- **التقارير المالية** - قائمة الدخل والميزانية العمومية والتدفق النقدي وميزان المراجعة
- **العملات المتعددة** - دعم أكثر من 150 عملة مع أسعار فورية
- **إدارة الضرائب** - حسابات تلقائية وتقارير امتثال

## 🔐 الأمان

### المصادقة والتفويض
- **مصادقة قائمة على JWT** - مصادقة آمنة قائمة على الرموز
- **التحكم في الوصول القائم على الأدوار** - نظام صلاحيات مفصل
- **المصادقة متعددة العوامل** - 2FA اختياري للأمان المعزز
- **إدارة الجلسات** - معالجة آمنة للجلسات مع انتهاء صلاحية تلقائي

### حماية البيانات
- **التشفير أثناء الراحة** - تشفير قاعدة البيانات للبيانات الحساسة
- **التشفير أثناء النقل** - TLS/SSL لجميع الاتصالات
- **التحقق من الإدخال** - التحقق الشامل من البيانات والتطهير
- **حماية من حقن SQL** - استعلامات معاملة وحماية ORM

## 🤝 المساهمة

نرحب بالمساهمات من المجتمع! إليك كيف يمكنك المساعدة:

### البدء
1. **فورك المستودع** على GitHub
2. **استنسخ الفورك** محلياً
3. **أنشئ فرع ميزة** من `develop`
4. **قم بإجراء تغييراتك** مع اختبارات مناسبة
5. **أرسل طلب سحب** مع وصف واضح

## 📄 الترخيص

هذا المشروع مرخص تحت رخصة MIT - راجع ملف [LICENSE](LICENSE) للتفاصيل.

## 🆘 الدعم

### الوثائق
- **وثائق API**: متاحة في `/docs` عند تشغيل الخادم الخلفي
- **دليل المستخدم**: وثائق شاملة للمستخدم في مجلد `/docs`
- **دليل المطور**: وثائق تقنية للمساهمين

### الحصول على المساعدة
- **مشاكل GitHub**: الإبلاغ عن الأخطاء وطلب الميزات
- **المناقشات**: مناقشات المجتمع والأسئلة والأجوبة
- **دعم البريد الإلكتروني**: <EMAIL>

---

**تم البناء بـ ❤️ للمجتمع التجاري العالمي**

*حوّل عملك بنظام محاسبة حديث وآمن وقابل للتطوير.*
