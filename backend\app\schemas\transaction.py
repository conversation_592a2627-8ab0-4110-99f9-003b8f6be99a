"""
Transaction schemas.
"""

from pydantic import BaseModel, validator
from typing import Optional, List
from decimal import Decimal
from datetime import date


class TransactionEntryBase(BaseModel):
    """Base transaction entry schema."""
    account_id: int
    debit_amount: Decimal = Decimal('0')
    credit_amount: Decimal = Decimal('0')
    description: Optional[str] = None
    
    @validator('debit_amount', 'credit_amount')
    def validate_amounts(cls, v):
        if v < 0:
            raise ValueError('Amounts cannot be negative')
        return v
    
    @validator('credit_amount')
    def validate_entry_amounts(cls, v, values):
        debit_amount = values.get('debit_amount', Decimal('0'))
        if debit_amount > 0 and v > 0:
            raise ValueError('Entry cannot have both debit and credit amounts')
        if debit_amount == 0 and v == 0:
            raise ValueError('Entry must have either debit or credit amount')
        return v


class TransactionEntryCreate(TransactionEntryBase):
    """Transaction entry creation schema."""
    pass


class TransactionEntryResponse(TransactionEntryBase):
    """Transaction entry response schema."""
    id: int
    transaction_id: int
    amount: Decimal
    is_debit: bool
    is_credit: bool
    
    class Config:
        from_attributes = True


class TransactionBase(BaseModel):
    """Base transaction schema."""
    reference: str
    description: str
    transaction_date: date
    source_type: Optional[str] = None
    source_id: Optional[int] = None
    
    @validator('reference')
    def validate_reference(cls, v):
        if not v or len(v.strip()) == 0:
            raise ValueError('Transaction reference is required')
        return v.strip()
    
    @validator('description')
    def validate_description(cls, v):
        if not v or len(v.strip()) == 0:
            raise ValueError('Transaction description is required')
        return v.strip()


class TransactionCreate(TransactionBase):
    """Transaction creation schema."""
    entries: List[TransactionEntryCreate]
    
    @validator('entries')
    def validate_entries(cls, v):
        if not v or len(v) < 2:
            raise ValueError('Transaction must have at least 2 entries')
        
        # Check if transaction is balanced
        total_debits = sum(entry.debit_amount for entry in v)
        total_credits = sum(entry.credit_amount for entry in v)
        
        if total_debits != total_credits:
            raise ValueError('Transaction must be balanced (debits = credits)')
        
        return v


class TransactionUpdate(BaseModel):
    """Transaction update schema."""
    reference: Optional[str] = None
    description: Optional[str] = None
    transaction_date: Optional[date] = None
    entries: Optional[List[TransactionEntryCreate]] = None


class TransactionResponse(TransactionBase):
    """Transaction response schema."""
    id: int
    company_id: int
    created_by_id: int
    is_posted: bool
    is_reversed: bool
    total_debits: Decimal
    total_credits: Decimal
    is_balanced: bool
    entries: List[TransactionEntryResponse]
    
    class Config:
        from_attributes = True


class TransactionList(BaseModel):
    """Transaction list response schema."""
    transactions: List[TransactionResponse]
    total: int
    page: int
    size: int
    pages: int


class JournalEntryResponse(BaseModel):
    """Journal entry response schema."""
    id: int
    transaction_id: int
    transaction_reference: str
    transaction_date: date
    account_id: int
    account_code: str
    account_name: str
    description: str
    debit_amount: Decimal
    credit_amount: Decimal
    amount: Decimal
    is_debit: bool
