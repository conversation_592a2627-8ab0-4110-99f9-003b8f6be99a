"""
نظام المحاسبة العالمي - تطبيق مبسط للعرض التوضيحي
Global Accounting System - Simplified Demo Application
"""

from fastapi import FastAPI, HTTPException, Depends, status
from fastapi.middleware.cors import CORSMiddleware
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from fastapi.staticfiles import StaticFiles
from fastapi.responses import FileResponse
from pydantic import BaseModel, EmailStr
from typing import List, Optional
import os
import jwt
from datetime import datetime, timedelta
import json

# إعدادات التطبيق
app = FastAPI(
    title="نظام المحاسبة العالمي",
    description="نظام محاسبة شامل ومتطور - Global Accounting System",
    version="1.0.0",
    docs_url="/docs",
    redoc_url="/redoc"
)

# إعدادات CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# خدمة الملفات الثابتة
app.mount("/static", StaticFiles(directory="static"), name="static")

# إعدادات التطبيق
SECRET_KEY = "GlobalAccounting2024SecretKey"

# بيانات وهمية للعرض التوضيحي
DEMO_USER = {
    "id": "demo-user-123",
    "email": "<EMAIL>",
    "username": "admin",
    "first_name": "محمد",
    "last_name": "الإداري",
    "role": "super_admin"
}

DEMO_COMPANIES = [
    {"id": "comp-1", "name": "شركة المحاسبة العالمية", "email": "<EMAIL>", "phone": "+************"},
    {"id": "comp-2", "name": "مؤسسة التقنية المتقدمة", "email": "<EMAIL>", "phone": "+************"},
    {"id": "comp-3", "name": "مجموعة الأعمال الذكية", "email": "<EMAIL>", "phone": "+************"}
]

DEMO_ACCOUNTS = [
    {"id": "acc-1", "code": "1000", "name": "النقدية في الصندوق", "account_type": "asset", "current_balance": 50000.00},
    {"id": "acc-2", "code": "1100", "name": "النقدية في البنك", "account_type": "asset", "current_balance": 125000.00},
    {"id": "acc-3", "code": "1200", "name": "ذمم العملاء", "account_type": "asset", "current_balance": 75000.00},
    {"id": "acc-4", "code": "1300", "name": "المخزون", "account_type": "asset", "current_balance": 95000.00},
    {"id": "acc-5", "code": "2000", "name": "دائنو الموردين", "account_type": "liability", "current_balance": 45000.00},
    {"id": "acc-6", "code": "3000", "name": "رأس المال", "account_type": "equity", "current_balance": 200000.00},
    {"id": "acc-7", "code": "4000", "name": "إيرادات المبيعات", "account_type": "revenue", "current_balance": 150000.00},
    {"id": "acc-8", "code": "5000", "name": "تكلفة البضاعة المباعة", "account_type": "expense", "current_balance": 80000.00},
    {"id": "acc-9", "code": "6000", "name": "مصاريف تشغيلية", "account_type": "expense", "current_balance": 25000.00},
    {"id": "acc-10", "code": "6100", "name": "رواتب وأجور", "account_type": "expense", "current_balance": 35000.00}
]

# نماذج البيانات
class LoginRequest(BaseModel):
    username: str
    password: str

class UserResponse(BaseModel):
    id: str
    email: str
    username: str
    first_name: str
    last_name: str
    role: str

class CompanyResponse(BaseModel):
    id: str
    name: str
    email: Optional[str]
    phone: Optional[str]

class AccountResponse(BaseModel):
    id: str
    code: str
    name: str
    account_type: str
    current_balance: float

# مصادقة JWT
security = HTTPBearer()

def create_access_token(data: dict):
    to_encode = data.copy()
    expire = datetime.utcnow() + timedelta(hours=24)
    to_encode.update({"exp": expire})
    encoded_jwt = jwt.encode(to_encode, SECRET_KEY, algorithm="HS256")
    return encoded_jwt

def verify_token(credentials: HTTPAuthorizationCredentials = Depends(security)):
    try:
        payload = jwt.decode(credentials.credentials, SECRET_KEY, algorithms=["HS256"])
        return payload
    except jwt.PyJWTError:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid authentication credentials"
        )

# محاكاة اتصال قاعدة البيانات
async def get_db_connection():
    # محاكاة اتصال ناجح
    return True

# الصفحة الرئيسية
@app.get("/")
async def root():
    return FileResponse('static/index.html')

# معلومات API
@app.get("/api")
async def api_info():
    return {
        "message": "مرحباً بك في نظام المحاسبة العالمي",
        "message_en": "Welcome to Global Accounting System",
        "version": "1.0.0",
        "status": "running",
        "features": [
            "نظام محاسبة متكامل",
            "إدارة العملاء والموردين",
            "نظام الفواتير",
            "التقارير المالية",
            "دعم العملات المتعددة"
        ],
        "docs": "/docs",
        "database": "Connected to Supabase PostgreSQL"
    }

# فحص الصحة
@app.get("/health")
async def health_check():
    return {
        "status": "healthy",
        "service": "نظام المحاسبة العالمي",
        "database": "connected (demo mode)",
        "timestamp": datetime.now().isoformat()
    }

# تسجيل الدخول
@app.post("/api/v1/auth/login")
async def login(login_data: LoginRequest):
    # التحقق من بيانات المستخدم التجريبي
    if (login_data.username in ["admin", "<EMAIL>"] and
        login_data.password == "admin123"):

        token = create_access_token({
            "sub": DEMO_USER["id"],
            "username": DEMO_USER["username"]
        })

        return {
            "access_token": token,
            "token_type": "bearer",
            "user": DEMO_USER
        }
    else:
        raise HTTPException(
            status_code=401,
            detail="اسم المستخدم أو كلمة المرور غير صحيحة"
        )

# الحصول على المستخدم الحالي
@app.get("/api/v1/auth/me", response_model=UserResponse)
async def get_current_user(token_data: dict = Depends(verify_token)):
    return UserResponse(**DEMO_USER)

# الحصول على الشركات
@app.get("/api/v1/companies", response_model=List[CompanyResponse])
async def get_companies(token_data: dict = Depends(verify_token)):
    return [CompanyResponse(**company) for company in DEMO_COMPANIES]

# الحصول على الحسابات
@app.get("/api/v1/accounts", response_model=List[AccountResponse])
async def get_accounts(token_data: dict = Depends(verify_token)):
    return [AccountResponse(**account) for account in DEMO_ACCOUNTS]

# إحصائيات لوحة التحكم
@app.get("/api/v1/dashboard/stats")
async def get_dashboard_stats(token_data: dict = Depends(verify_token)):
    return {
        "customers_count": 15,
        "suppliers_count": 8,
        "products_count": 45,
        "invoices_count": 127,
        "total_revenue": 125430.50,
        "total_expenses": 89240.25,
        "net_profit": 36190.25,
        "currency": "SAR"
    }

# معلومات النظام
@app.get("/api/v1/system/info")
async def get_system_info():
    return {
        "system_name": "نظام المحاسبة العالمي",
        "system_name_en": "Global Accounting System",
        "version": "1.0.0",
        "developer": "فريق التطوير العالمي",
        "features": {
            "accounting": "نظام محاسبة متكامل",
            "invoicing": "إدارة الفواتير",
            "inventory": "إدارة المخزون",
            "crm": "إدارة العملاء",
            "reports": "التقارير المالية",
            "multi_currency": "دعم العملات المتعددة",
            "multi_company": "دعم الشركات المتعددة"
        },
        "database": {
            "type": "PostgreSQL",
            "provider": "Supabase",
            "status": "connected"
        },
        "api_docs": "/docs"
    }

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)
