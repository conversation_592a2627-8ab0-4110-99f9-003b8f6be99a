"""
Reports endpoints.
"""

from typing import Optional
from fastapi import APIRouter, Depends, HTTPException, status, Query, Response
from sqlalchemy.ext.asyncio import AsyncSession
from datetime import date

from app.core.database import get_db
from app.core.security import get_current_active_user, require_permissions
from app.models.user import User
from app.services.reports_service import ReportsService

router = APIRouter()


@router.get("/profit-loss")
@require_permissions("view_reports")
async def get_profit_loss_report(
    start_date: date = Query(..., description="Start date for the report"),
    end_date: date = Query(..., description="End date for the report"),
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """Get Profit & Loss statement."""
    reports_service = ReportsService(db)
    report = await reports_service.get_profit_loss_report(
        current_user.company_id, start_date, end_date
    )
    return report


@router.get("/balance-sheet")
@require_permissions("view_reports")
async def get_balance_sheet_report(
    as_of_date: date = Query(..., description="As of date for the balance sheet"),
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """Get Balance Sheet."""
    reports_service = ReportsService(db)
    report = await reports_service.get_balance_sheet_report(
        current_user.company_id, as_of_date
    )
    return report


@router.get("/cash-flow")
@require_permissions("view_reports")
async def get_cash_flow_report(
    start_date: date = Query(..., description="Start date for the report"),
    end_date: date = Query(..., description="End date for the report"),
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """Get Cash Flow statement."""
    reports_service = ReportsService(db)
    report = await reports_service.get_cash_flow_report(
        current_user.company_id, start_date, end_date
    )
    return report


@router.get("/trial-balance")
@require_permissions("view_reports")
async def get_trial_balance_report(
    as_of_date: date = Query(..., description="As of date for the trial balance"),
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """Get Trial Balance."""
    reports_service = ReportsService(db)
    report = await reports_service.get_trial_balance_report(
        current_user.company_id, as_of_date
    )
    return report


@router.get("/general-ledger")
@require_permissions("view_reports")
async def get_general_ledger_report(
    start_date: date = Query(..., description="Start date for the report"),
    end_date: date = Query(..., description="End date for the report"),
    account_id: Optional[int] = Query(None, description="Specific account ID"),
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """Get General Ledger report."""
    reports_service = ReportsService(db)
    report = await reports_service.get_general_ledger_report(
        current_user.company_id, start_date, end_date, account_id
    )
    return report


@router.get("/accounts-receivable")
@require_permissions("view_reports")
async def get_accounts_receivable_report(
    as_of_date: date = Query(..., description="As of date for the report"),
    aging: bool = Query(False, description="Include aging analysis"),
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """Get Accounts Receivable report."""
    reports_service = ReportsService(db)
    report = await reports_service.get_accounts_receivable_report(
        current_user.company_id, as_of_date, aging
    )
    return report


@router.get("/accounts-payable")
@require_permissions("view_reports")
async def get_accounts_payable_report(
    as_of_date: date = Query(..., description="As of date for the report"),
    aging: bool = Query(False, description="Include aging analysis"),
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """Get Accounts Payable report."""
    reports_service = ReportsService(db)
    report = await reports_service.get_accounts_payable_report(
        current_user.company_id, as_of_date, aging
    )
    return report


@router.get("/sales-summary")
@require_permissions("view_reports")
async def get_sales_summary_report(
    start_date: date = Query(..., description="Start date for the report"),
    end_date: date = Query(..., description="End date for the report"),
    group_by: str = Query("month", regex="^(day|week|month|quarter|year)$"),
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """Get Sales Summary report."""
    reports_service = ReportsService(db)
    report = await reports_service.get_sales_summary_report(
        current_user.company_id, start_date, end_date, group_by
    )
    return report


@router.get("/inventory-valuation")
@require_permissions("view_reports")
async def get_inventory_valuation_report(
    as_of_date: date = Query(..., description="As of date for the report"),
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """Get Inventory Valuation report."""
    reports_service = ReportsService(db)
    report = await reports_service.get_inventory_valuation_report(
        current_user.company_id, as_of_date
    )
    return report


@router.get("/tax-summary")
@require_permissions("view_reports")
async def get_tax_summary_report(
    start_date: date = Query(..., description="Start date for the report"),
    end_date: date = Query(..., description="End date for the report"),
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """Get Tax Summary report."""
    reports_service = ReportsService(db)
    report = await reports_service.get_tax_summary_report(
        current_user.company_id, start_date, end_date
    )
    return report


@router.get("/{report_type}/export")
@require_permissions("export_data")
async def export_report(
    report_type: str,
    format: str = Query("pdf", regex="^(pdf|excel|csv)$"),
    start_date: Optional[date] = Query(None),
    end_date: Optional[date] = Query(None),
    as_of_date: Optional[date] = Query(None),
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """Export report in specified format."""
    reports_service = ReportsService(db)
    
    # Validate required parameters based on report type
    if report_type in ["profit-loss", "cash-flow", "sales-summary", "tax-summary"]:
        if not start_date or not end_date:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="start_date and end_date are required for this report"
            )
    elif report_type in ["balance-sheet", "trial-balance", "accounts-receivable", "accounts-payable", "inventory-valuation"]:
        if not as_of_date:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="as_of_date is required for this report"
            )
    
    # Generate export
    file_data, filename, content_type = await reports_service.export_report(
        report_type=report_type,
        company_id=current_user.company_id,
        format=format,
        start_date=start_date,
        end_date=end_date,
        as_of_date=as_of_date
    )
    
    return Response(
        content=file_data,
        media_type=content_type,
        headers={"Content-Disposition": f"attachment; filename={filename}"}
    )


@router.get("/custom/builder")
@require_permissions("view_reports")
async def get_custom_report_builder_data(
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """Get data for custom report builder."""
    reports_service = ReportsService(db)
    data = await reports_service.get_report_builder_data(current_user.company_id)
    return data


@router.post("/custom/generate")
@require_permissions("view_reports")
async def generate_custom_report(
    report_config: dict,
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """Generate custom report based on configuration."""
    reports_service = ReportsService(db)
    report = await reports_service.generate_custom_report(
        current_user.company_id, report_config
    )
    return report
