import axios, { AxiosInstance, AxiosResponse } from 'axios'
import toast from 'react-hot-toast'

// API Base URL
const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000'

// Create axios instance
const api: AxiosInstance = axios.create({
  baseURL: `${API_BASE_URL}/api/v1`,
  headers: {
    'Content-Type': 'application/json',
  },
})

// Request interceptor to add auth token
api.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('access_token')
    if (token) {
      config.headers.Authorization = `Bearer ${token}`
    }
    return config
  },
  (error) => {
    return Promise.reject(error)
  }
)

// Response interceptor to handle token refresh
api.interceptors.response.use(
  (response) => response,
  async (error) => {
    const originalRequest = error.config

    if (error.response?.status === 401 && !originalRequest._retry) {
      originalRequest._retry = true

      try {
        const refreshToken = localStorage.getItem('refresh_token')
        if (refreshToken) {
          const response = await axios.post(`${API_BASE_URL}/api/v1/auth/refresh`, {
            refresh_token: refreshToken,
          })

          const { access_token, refresh_token: newRefreshToken } = response.data
          localStorage.setItem('access_token', access_token)
          localStorage.setItem('refresh_token', newRefreshToken)

          // Retry original request
          originalRequest.headers.Authorization = `Bearer ${access_token}`
          return api(originalRequest)
        }
      } catch (refreshError) {
        // Refresh failed, redirect to login
        localStorage.removeItem('access_token')
        localStorage.removeItem('refresh_token')
        window.location.href = '/auth/login'
        return Promise.reject(refreshError)
      }
    }

    return Promise.reject(error)
  }
)

// Auth API
export const authApi = {
  login: async (username: string, password: string) => {
    const formData = new FormData()
    formData.append('username', username)
    formData.append('password', password)
    
    const response = await api.post('/auth/login', formData, {
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
      },
    })
    return response.data
  },

  register: async (userData: {
    email: string
    username: string
    first_name: string
    last_name: string
    password: string
    phone?: string
  }) => {
    const response = await api.post('/auth/register', userData)
    return response.data
  },

  getCurrentUser: async () => {
    const response = await api.get('/auth/me')
    return response.data
  },

  updateProfile: async (userData: any) => {
    const response = await api.put('/auth/me', userData)
    return response.data
  },

  changePassword: async (currentPassword: string, newPassword: string) => {
    const response = await api.post('/auth/change-password', {
      current_password: currentPassword,
      new_password: newPassword,
    })
    return response.data
  },
}

// Users API
export const usersApi = {
  getUsers: async (params?: any) => {
    const response = await api.get('/users', { params })
    return response.data
  },

  getUser: async (id: number) => {
    const response = await api.get(`/users/${id}`)
    return response.data
  },

  createUser: async (userData: any) => {
    const response = await api.post('/users', userData)
    return response.data
  },

  updateUser: async (id: number, userData: any) => {
    const response = await api.put(`/users/${id}`, userData)
    return response.data
  },

  deleteUser: async (id: number) => {
    const response = await api.delete(`/users/${id}`)
    return response.data
  },
}

// Companies API
export const companiesApi = {
  getCompanies: async (params?: any) => {
    const response = await api.get('/companies', { params })
    return response.data
  },

  getCompany: async (id: number) => {
    const response = await api.get(`/companies/${id}`)
    return response.data
  },

  createCompany: async (companyData: any) => {
    const response = await api.post('/companies', companyData)
    return response.data
  },

  updateCompany: async (id: number, companyData: any) => {
    const response = await api.put(`/companies/${id}`, companyData)
    return response.data
  },

  deleteCompany: async (id: number) => {
    const response = await api.delete(`/companies/${id}`)
    return response.data
  },
}

// Accounts API
export const accountsApi = {
  getAccounts: async (params?: any) => {
    const response = await api.get('/accounts', { params })
    return response.data
  },

  getAccount: async (id: number) => {
    const response = await api.get(`/accounts/${id}`)
    return response.data
  },

  createAccount: async (accountData: any) => {
    const response = await api.post('/accounts', accountData)
    return response.data
  },

  updateAccount: async (id: number, accountData: any) => {
    const response = await api.put(`/accounts/${id}`, accountData)
    return response.data
  },

  deleteAccount: async (id: number) => {
    const response = await api.delete(`/accounts/${id}`)
    return response.data
  },

  getChartOfAccounts: async () => {
    const response = await api.get('/accounts/chart')
    return response.data
  },
}

// Transactions API
export const transactionsApi = {
  getTransactions: async (params?: any) => {
    const response = await api.get('/transactions', { params })
    return response.data
  },

  getTransaction: async (id: number) => {
    const response = await api.get(`/transactions/${id}`)
    return response.data
  },

  createTransaction: async (transactionData: any) => {
    const response = await api.post('/transactions', transactionData)
    return response.data
  },

  updateTransaction: async (id: number, transactionData: any) => {
    const response = await api.put(`/transactions/${id}`, transactionData)
    return response.data
  },

  deleteTransaction: async (id: number) => {
    const response = await api.delete(`/transactions/${id}`)
    return response.data
  },

  postTransaction: async (id: number) => {
    const response = await api.post(`/transactions/${id}/post`)
    return response.data
  },
}

// Customers API
export const customersApi = {
  getCustomers: async (params?: any) => {
    const response = await api.get('/customers', { params })
    return response.data
  },

  getCustomer: async (id: number) => {
    const response = await api.get(`/customers/${id}`)
    return response.data
  },

  createCustomer: async (customerData: any) => {
    const response = await api.post('/customers', customerData)
    return response.data
  },

  updateCustomer: async (id: number, customerData: any) => {
    const response = await api.put(`/customers/${id}`, customerData)
    return response.data
  },

  deleteCustomer: async (id: number) => {
    const response = await api.delete(`/customers/${id}`)
    return response.data
  },
}

// Suppliers API
export const suppliersApi = {
  getSuppliers: async (params?: any) => {
    const response = await api.get('/suppliers', { params })
    return response.data
  },

  getSupplier: async (id: number) => {
    const response = await api.get(`/suppliers/${id}`)
    return response.data
  },

  createSupplier: async (supplierData: any) => {
    const response = await api.post('/suppliers', supplierData)
    return response.data
  },

  updateSupplier: async (id: number, supplierData: any) => {
    const response = await api.put(`/suppliers/${id}`, supplierData)
    return response.data
  },

  deleteSupplier: async (id: number) => {
    const response = await api.delete(`/suppliers/${id}`)
    return response.data
  },
}

// Products API
export const productsApi = {
  getProducts: async (params?: any) => {
    const response = await api.get('/products', { params })
    return response.data
  },

  getProduct: async (id: number) => {
    const response = await api.get(`/products/${id}`)
    return response.data
  },

  createProduct: async (productData: any) => {
    const response = await api.post('/products', productData)
    return response.data
  },

  updateProduct: async (id: number, productData: any) => {
    const response = await api.put(`/products/${id}`, productData)
    return response.data
  },

  deleteProduct: async (id: number) => {
    const response = await api.delete(`/products/${id}`)
    return response.data
  },
}

// Invoices API
export const invoicesApi = {
  getInvoices: async (params?: any) => {
    const response = await api.get('/invoices', { params })
    return response.data
  },

  getInvoice: async (id: number) => {
    const response = await api.get(`/invoices/${id}`)
    return response.data
  },

  createInvoice: async (invoiceData: any) => {
    const response = await api.post('/invoices', invoiceData)
    return response.data
  },

  updateInvoice: async (id: number, invoiceData: any) => {
    const response = await api.put(`/invoices/${id}`, invoiceData)
    return response.data
  },

  deleteInvoice: async (id: number) => {
    const response = await api.delete(`/invoices/${id}`)
    return response.data
  },

  sendInvoice: async (id: number) => {
    const response = await api.post(`/invoices/${id}/send`)
    return response.data
  },

  generatePDF: async (id: number) => {
    const response = await api.get(`/invoices/${id}/pdf`, {
      responseType: 'blob',
    })
    return response.data
  },
}

// Dashboard API
export const dashboardApi = {
  getStats: async () => {
    const response = await api.get('/dashboard/stats')
    return response.data
  },

  getRecentActivity: async () => {
    const response = await api.get('/dashboard/activity')
    return response.data
  },

  getChartData: async (type: string, period: string) => {
    const response = await api.get(`/dashboard/charts/${type}`, {
      params: { period },
    })
    return response.data
  },
}

// Reports API
export const reportsApi = {
  getProfitLoss: async (params: any) => {
    const response = await api.get('/reports/profit-loss', { params })
    return response.data
  },

  getBalanceSheet: async (params: any) => {
    const response = await api.get('/reports/balance-sheet', { params })
    return response.data
  },

  getCashFlow: async (params: any) => {
    const response = await api.get('/reports/cash-flow', { params })
    return response.data
  },

  getTrialBalance: async (params: any) => {
    const response = await api.get('/reports/trial-balance', { params })
    return response.data
  },

  exportReport: async (reportType: string, params: any, format: 'pdf' | 'excel') => {
    const response = await api.get(`/reports/${reportType}/export`, {
      params: { ...params, format },
      responseType: 'blob',
    })
    return response.data
  },
}

export default api
