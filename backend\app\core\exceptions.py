"""
Custom exceptions and exception handlers.
"""

from fastapi import <PERSON><PERSON><PERSON>, Request, HTTPException
from fastapi.responses import JSONResponse
from fastapi.exceptions import RequestValidationError
from sqlalchemy.exc import SQLAlchemyError
from starlette.exceptions import HTTPException as StarletteHTTPException
import logging
from typing import Union

logger = logging.getLogger(__name__)


class AccountingException(Exception):
    """Base exception for accounting-related errors."""
    
    def __init__(self, message: str, code: str = "ACCOUNTING_ERROR"):
        self.message = message
        self.code = code
        super().__init__(self.message)


class InsufficientFundsException(AccountingException):
    """Exception raised when there are insufficient funds for a transaction."""
    
    def __init__(self, message: str = "Insufficient funds for this transaction"):
        super().__init__(message, "INSUFFICIENT_FUNDS")


class InvalidAccountException(AccountingException):
    """Exception raised when an invalid account is used."""
    
    def __init__(self, message: str = "Invalid account specified"):
        super().__init__(message, "INVALID_ACCOUNT")


class DuplicateEntryException(AccountingException):
    """Exception raised when a duplicate entry is attempted."""
    
    def __init__(self, message: str = "Duplicate entry not allowed"):
        super().__init__(message, "DUPLICATE_ENTRY")


class PermissionDeniedException(AccountingException):
    """Exception raised when user lacks permission for an operation."""
    
    def __init__(self, message: str = "Permission denied"):
        super().__init__(message, "PERMISSION_DENIED")


class ValidationException(AccountingException):
    """Exception raised when data validation fails."""
    
    def __init__(self, message: str = "Data validation failed"):
        super().__init__(message, "VALIDATION_ERROR")


async def accounting_exception_handler(request: Request, exc: AccountingException):
    """Handle custom accounting exceptions."""
    logger.error(f"Accounting error: {exc.message} (Code: {exc.code})")
    return JSONResponse(
        status_code=400,
        content={
            "error": exc.code,
            "message": exc.message,
            "type": "accounting_error"
        }
    )


async def http_exception_handler(request: Request, exc: Union[HTTPException, StarletteHTTPException]):
    """Handle HTTP exceptions."""
    logger.error(f"HTTP error {exc.status_code}: {exc.detail}")
    return JSONResponse(
        status_code=exc.status_code,
        content={
            "error": f"HTTP_{exc.status_code}",
            "message": exc.detail,
            "type": "http_error"
        }
    )


async def validation_exception_handler(request: Request, exc: RequestValidationError):
    """Handle request validation errors."""
    logger.error(f"Validation error: {exc.errors()}")
    return JSONResponse(
        status_code=422,
        content={
            "error": "VALIDATION_ERROR",
            "message": "Request validation failed",
            "details": exc.errors(),
            "type": "validation_error"
        }
    )


async def database_exception_handler(request: Request, exc: SQLAlchemyError):
    """Handle database errors."""
    logger.error(f"Database error: {str(exc)}")
    return JSONResponse(
        status_code=500,
        content={
            "error": "DATABASE_ERROR",
            "message": "A database error occurred",
            "type": "database_error"
        }
    )


async def general_exception_handler(request: Request, exc: Exception):
    """Handle general exceptions."""
    logger.error(f"Unexpected error: {str(exc)}", exc_info=True)
    return JSONResponse(
        status_code=500,
        content={
            "error": "INTERNAL_SERVER_ERROR",
            "message": "An unexpected error occurred",
            "type": "server_error"
        }
    )


def setup_exception_handlers(app: FastAPI):
    """Setup all exception handlers for the FastAPI app."""
    app.add_exception_handler(AccountingException, accounting_exception_handler)
    app.add_exception_handler(HTTPException, http_exception_handler)
    app.add_exception_handler(StarletteHTTPException, http_exception_handler)
    app.add_exception_handler(RequestValidationError, validation_exception_handler)
    app.add_exception_handler(SQLAlchemyError, database_exception_handler)
    app.add_exception_handler(Exception, general_exception_handler)
