"""
Companies endpoints.
"""

from typing import List, Optional
from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.ext.asyncio import AsyncSession

from app.core.database import get_db
from app.core.security import get_current_active_user, require_permissions
from app.models.user import User
from app.services.company_service import CompanyService
from app.schemas.company import CompanyResponse, CompanyCreate, CompanyUpdate, CompanyList

router = APIRouter()


@router.get("/", response_model=CompanyList)
async def get_companies(
    skip: int = Query(0, ge=0),
    limit: int = Query(20, ge=1, le=100),
    search: Optional[str] = Query(None),
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """Get list of companies with pagination and filtering."""
    company_service = CompanyService(db)
    
    # Build filters
    filters = {}
    if not current_user.has_permission("view_all_data"):
        # Non-admin users can only see their own company
        filters["id"] = current_user.company_id
    
    # Get companies
    if search:
        companies = await company_service.search_companies(search)
        total = len(companies)
        companies = companies[skip:skip + limit]
    else:
        companies = await company_service.get_multi(skip=skip, limit=limit, filters=filters)
        total = await company_service.count(filters)
    
    return CompanyList(
        companies=companies,
        total=total,
        page=skip // limit + 1,
        size=limit,
        pages=(total + limit - 1) // limit
    )


@router.get("/{company_id}", response_model=CompanyResponse)
async def get_company(
    company_id: int,
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """Get company by ID."""
    company_service = CompanyService(db)
    company = await company_service.get_by_id(company_id)
    
    if not company:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Company not found"
        )
    
    # Check permissions
    if (company_id != current_user.company_id and 
        not current_user.has_permission("view_all_data")):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Not enough permissions"
        )
    
    return company


@router.post("/", response_model=CompanyResponse)
@require_permissions("manage_companies")
async def create_company(
    company_data: CompanyCreate,
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """Create a new company."""
    company_service = CompanyService(db)
    
    # Check if company name already exists
    existing_company = await company_service.get_by_name(company_data.name)
    if existing_company:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Company name already exists"
        )
    
    # Create company
    company = await company_service.create(company_data.dict())
    
    # Create default settings for the company
    await company_service.create_default_settings(company.id)
    
    return company


@router.put("/{company_id}", response_model=CompanyResponse)
async def update_company(
    company_id: int,
    company_data: CompanyUpdate,
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """Update company."""
    company_service = CompanyService(db)
    company = await company_service.get_by_id(company_id)
    
    if not company:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Company not found"
        )
    
    # Check permissions
    if (company_id != current_user.company_id and 
        not current_user.has_permission("manage_companies")):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Not enough permissions"
        )
    
    # Update company
    updated_company = await company_service.update(company_id, company_data.dict(exclude_unset=True))
    return updated_company


@router.delete("/{company_id}")
@require_permissions("manage_companies")
async def delete_company(
    company_id: int,
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """Delete company."""
    if company_id == current_user.company_id:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Cannot delete your own company"
        )
    
    company_service = CompanyService(db)
    success = await company_service.delete(company_id)
    
    if not success:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Company not found"
        )
    
    return {"message": "Company deleted successfully"}


@router.get("/{company_id}/settings")
async def get_company_settings(
    company_id: int,
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """Get company settings."""
    # Check permissions
    if (company_id != current_user.company_id and 
        not current_user.has_permission("view_all_data")):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Not enough permissions"
        )
    
    company_service = CompanyService(db)
    settings = await company_service.get_settings(company_id)
    
    if not settings:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Company settings not found"
        )
    
    return settings


@router.put("/{company_id}/settings")
async def update_company_settings(
    company_id: int,
    settings_data: dict,
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """Update company settings."""
    # Check permissions
    if (company_id != current_user.company_id and 
        not current_user.has_permission("manage_settings")):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Not enough permissions"
        )
    
    company_service = CompanyService(db)
    settings = await company_service.update_settings(company_id, settings_data)
    
    if not settings:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Company settings not found"
        )
    
    return settings


@router.post("/{company_id}/initialize-chart-of-accounts")
async def initialize_chart_of_accounts(
    company_id: int,
    template: str = "standard",
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """Initialize chart of accounts for a company."""
    # Check permissions
    if (company_id != current_user.company_id and 
        not current_user.has_permission("manage_accounts")):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Not enough permissions"
        )
    
    company_service = CompanyService(db)
    success = await company_service.initialize_chart_of_accounts(company_id, template)
    
    if not success:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Failed to initialize chart of accounts"
        )
    
    return {"message": "Chart of accounts initialized successfully"}
