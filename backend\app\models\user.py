"""
User model and related classes.
"""

from sqlalchemy import <PERSON>umn, Integer, String, Boolean, Enum, ForeignKey, Text
from sqlalchemy.orm import relationship
from enum import Enum as PyEnum
from typing import List

from .base import BaseModel


class UserRole(PyEnum):
    """User roles in the system."""
    SUPER_ADMIN = "super_admin"
    ADMIN = "admin"
    ACCOUNTANT = "accountant"
    MANAGER = "manager"
    USER = "user"
    VIEWER = "viewer"


class User(BaseModel):
    """User model."""
    
    __tablename__ = "users"
    
    # Basic information
    email = Column(String(255), unique=True, index=True, nullable=False)
    username = Column(String(100), unique=True, index=True, nullable=False)
    first_name = Column(String(100), nullable=False)
    last_name = Column(String(100), nullable=False)
    
    # Authentication
    hashed_password = Column(String(255), nullable=False)
    is_active = Column(Boolean, default=True, nullable=False)
    is_verified = Column(Boolean, default=False, nullable=False)
    
    # Role and permissions
    role = Column(Enum(UserRole), default=UserRole.USER, nullable=False)
    
    # Profile information
    phone = Column(String(20), nullable=True)
    avatar_url = Column(String(500), nullable=True)
    bio = Column(Text, nullable=True)
    
    # Company association
    company_id = Column(Integer, ForeignKey("companies.id"), nullable=True)
    
    # Relationships
    company = relationship("Company", back_populates="users")
    
    @property
    def full_name(self) -> str:
        """Get user's full name."""
        return f"{self.first_name} {self.last_name}"
    
    def get_permissions(self) -> List[str]:
        """Get user permissions based on role."""
        permissions_map = {
            UserRole.SUPER_ADMIN: [
                "manage_users", "manage_companies", "manage_settings",
                "view_all_data", "edit_all_data", "delete_all_data",
                "manage_accounts", "manage_transactions", "manage_invoices",
                "manage_customers", "manage_suppliers", "manage_inventory",
                "view_reports", "export_data"
            ],
            UserRole.ADMIN: [
                "manage_users", "manage_settings", "view_all_data",
                "edit_all_data", "manage_accounts", "manage_transactions",
                "manage_invoices", "manage_customers", "manage_suppliers",
                "manage_inventory", "view_reports", "export_data"
            ],
            UserRole.ACCOUNTANT: [
                "view_all_data", "edit_all_data", "manage_accounts",
                "manage_transactions", "manage_invoices", "view_reports",
                "export_data"
            ],
            UserRole.MANAGER: [
                "view_all_data", "manage_customers", "manage_suppliers",
                "manage_inventory", "view_reports", "export_data"
            ],
            UserRole.USER: [
                "view_own_data", "edit_own_data", "create_invoices",
                "view_basic_reports"
            ],
            UserRole.VIEWER: [
                "view_own_data", "view_basic_reports"
            ]
        }
        return permissions_map.get(self.role, [])
    
    def has_permission(self, permission: str) -> bool:
        """Check if user has a specific permission."""
        return permission in self.get_permissions()
    
    def can_access_company(self, company_id: int) -> bool:
        """Check if user can access a specific company."""
        if self.role in [UserRole.SUPER_ADMIN]:
            return True
        return self.company_id == company_id
