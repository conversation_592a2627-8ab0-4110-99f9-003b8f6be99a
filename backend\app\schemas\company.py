"""
Company schemas.
"""

from pydantic import BaseModel, EmailStr
from typing import Optional
from datetime import date


class CompanyBase(BaseModel):
    """Base company schema."""
    name: str
    legal_name: Optional[str] = None
    registration_number: Optional[str] = None
    tax_id: Optional[str] = None
    email: Optional[EmailStr] = None
    phone: Optional[str] = None
    website: Optional[str] = None
    address_line1: Optional[str] = None
    address_line2: Optional[str] = None
    city: Optional[str] = None
    state: Optional[str] = None
    postal_code: Optional[str] = None
    country: Optional[str] = None
    industry: Optional[str] = None
    description: Optional[str] = None
    logo_url: Optional[str] = None
    default_currency: str = "USD"
    fiscal_year_start: Optional[date] = None
    timezone: str = "UTC"


class CompanyCreate(CompanyBase):
    """Company creation schema."""
    pass


class CompanyUpdate(BaseModel):
    """Company update schema."""
    name: Optional[str] = None
    legal_name: Optional[str] = None
    registration_number: Optional[str] = None
    tax_id: Optional[str] = None
    email: Optional[EmailStr] = None
    phone: Optional[str] = None
    website: Optional[str] = None
    address_line1: Optional[str] = None
    address_line2: Optional[str] = None
    city: Optional[str] = None
    state: Optional[str] = None
    postal_code: Optional[str] = None
    country: Optional[str] = None
    industry: Optional[str] = None
    description: Optional[str] = None
    logo_url: Optional[str] = None
    default_currency: Optional[str] = None
    fiscal_year_start: Optional[date] = None
    timezone: Optional[str] = None
    is_active: Optional[bool] = None


class CompanyResponse(CompanyBase):
    """Company response schema."""
    id: int
    full_address: str
    is_active: bool
    
    class Config:
        from_attributes = True


class CompanyList(BaseModel):
    """Company list response schema."""
    companies: list[CompanyResponse]
    total: int
    page: int
    size: int
    pages: int


class CompanySettingsResponse(BaseModel):
    """Company settings response schema."""
    id: int
    company_id: int
    chart_of_accounts_template: str
    enable_multi_currency: bool
    auto_create_accounts: bool
    invoice_prefix: str
    invoice_number_format: str
    next_invoice_number: int
    default_tax_rate: str
    tax_inclusive_pricing: bool
    enable_inventory: bool
    inventory_valuation_method: str
    low_stock_threshold: int
    email_notifications: bool
    low_stock_alerts: bool
    payment_reminders: bool
    default_date_format: str
    default_number_format: str
    custom_settings: Optional[dict] = None
    
    class Config:
        from_attributes = True
