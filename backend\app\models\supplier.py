"""
Supplier model for vendor management.
"""

from sqlalchemy import Column, Integer, String, Text, Boolean, ForeignKey, Numeric
from sqlalchemy.orm import relationship
from decimal import Decimal

from .base import BaseModel


class Supplier(BaseModel):
    """Supplier/Vendor model."""
    
    __tablename__ = "suppliers"
    
    # Basic information
    supplier_code = Column(String(50), nullable=False, index=True)
    name = Column(String(255), nullable=False, index=True)
    company_name = Column(String(255), nullable=True)
    
    # Contact information
    email = Column(String(255), nullable=True)
    phone = Column(String(20), nullable=True)
    mobile = Column(String(20), nullable=True)
    website = Column(String(255), nullable=True)
    
    # Contact person
    contact_person = Column(String(255), nullable=True)
    contact_title = Column(String(100), nullable=True)
    
    # Address
    address_line1 = Column(String(255), nullable=True)
    address_line2 = Column(String(255), nullable=True)
    city = Column(String(100), nullable=True)
    state = Column(String(100), nullable=True)
    postal_code = Column(String(20), nullable=True)
    country = Column(String(100), nullable=True)
    
    # Business information
    tax_id = Column(String(100), nullable=True)
    registration_number = Column(String(100), nullable=True)
    
    # Financial settings
    payment_terms_days = Column(Integer, default=30, nullable=False)
    currency = Column(String(3), default="USD", nullable=False)
    
    # Banking information
    bank_name = Column(String(255), nullable=True)
    bank_account_number = Column(String(100), nullable=True)
    bank_routing_number = Column(String(50), nullable=True)
    swift_code = Column(String(20), nullable=True)
    
    # Status
    is_active = Column(Boolean, default=True, nullable=False)
    is_preferred = Column(Boolean, default=False, nullable=False)
    
    # Notes
    notes = Column(Text, nullable=True)
    
    # Company association
    company_id = Column(Integer, ForeignKey("companies.id"), nullable=False)
    
    # Relationships
    company = relationship("Company", back_populates="suppliers")
    purchase_invoices = relationship("Invoice", back_populates="supplier")
    
    @property
    def full_address(self) -> str:
        """Get formatted address."""
        address_parts = [
            self.address_line1,
            self.address_line2,
            self.city,
            self.state,
            self.postal_code,
            self.country
        ]
        return ", ".join(filter(None, address_parts))
    
    @property
    def display_name(self) -> str:
        """Get display name for supplier."""
        if self.company_name:
            return f"{self.company_name} ({self.name})"
        return self.name
    
    def get_outstanding_balance(self) -> Decimal:
        """Calculate outstanding balance from unpaid purchase invoices."""
        outstanding = Decimal('0')
        for invoice in self.purchase_invoices:
            if invoice.status in ['received', 'overdue']:
                outstanding += invoice.total_amount - invoice.paid_amount
        return outstanding
    
    def get_total_purchases_ytd(self) -> Decimal:
        """Get total purchases year-to-date."""
        from datetime import date
        current_year = date.today().year
        
        total = Decimal('0')
        for invoice in self.purchase_invoices:
            if invoice.invoice_date.year == current_year:
                total += invoice.total_amount
        return total
    
    def get_overdue_invoices(self):
        """Get list of overdue purchase invoices."""
        return [invoice for invoice in self.purchase_invoices if invoice.is_overdue()]
