"""
Inventory models for stock tracking.
"""

from sqlalchemy import Column, Integer, String, Text, Date, ForeignKey, Numeric, Enum
from sqlalchemy.orm import relationship
from enum import Enum as PyEnum
from decimal import Decimal
from datetime import date

from .base import BaseModel


class MovementType(PyEnum):
    """Inventory movement types."""
    IN = "in"
    OUT = "out"
    ADJUSTMENT = "adjustment"
    TRANSFER = "transfer"


class InventoryItem(BaseModel):
    """Inventory item model for tracking stock levels."""
    
    __tablename__ = "inventory_items"
    
    # Product reference
    product_id = Column(Integer, ForeignKey("products.id"), nullable=False)
    
    # Location (warehouse, store, etc.)
    location = Column(String(100), default="Main", nullable=False)
    
    # Stock levels
    quantity_on_hand = Column(Numeric(15, 3), default=0, nullable=False)
    quantity_reserved = Column(Numeric(15, 3), default=0, nullable=False)
    quantity_available = Column(Numeric(15, 3), default=0, nullable=False)
    
    # Cost tracking
    average_cost = Column(Numeric(15, 2), default=0, nullable=False)
    last_cost = Column(Numeric(15, 2), default=0, nullable=False)
    
    # Reorder information
    reorder_point = Column(Numeric(15, 3), default=0, nullable=False)
    reorder_quantity = Column(Numeric(15, 3), default=0, nullable=False)
    
    # Company association
    company_id = Column(Integer, ForeignKey("companies.id"), nullable=False)
    
    # Relationships
    company = relationship("Company")
    product = relationship("Product", back_populates="inventory_items")
    movements = relationship("InventoryMovement", back_populates="inventory_item")
    
    def update_quantities(self) -> None:
        """Update calculated quantities."""
        self.quantity_available = self.quantity_on_hand - self.quantity_reserved
    
    def is_below_reorder_point(self) -> bool:
        """Check if stock is below reorder point."""
        return self.quantity_available <= self.reorder_point
    
    def update_average_cost(self, new_quantity: Decimal, new_cost: Decimal) -> None:
        """Update average cost using weighted average method."""
        if new_quantity <= 0:
            return
        
        current_value = self.quantity_on_hand * self.average_cost
        new_value = new_quantity * new_cost
        total_quantity = self.quantity_on_hand + new_quantity
        
        if total_quantity > 0:
            self.average_cost = (current_value + new_value) / total_quantity
        
        self.last_cost = new_cost


class InventoryMovement(BaseModel):
    """Inventory movement model for tracking stock changes."""
    
    __tablename__ = "inventory_movements"
    
    # Inventory item reference
    inventory_item_id = Column(Integer, ForeignKey("inventory_items.id"), nullable=False)
    
    # Movement details
    movement_type = Column(Enum(MovementType), nullable=False)
    quantity = Column(Numeric(15, 3), nullable=False)
    unit_cost = Column(Numeric(15, 2), default=0, nullable=False)
    
    # Movement date
    movement_date = Column(Date, default=date.today, nullable=False)
    
    # Reference information
    reference_type = Column(String(50), nullable=True)  # invoice, adjustment, transfer, etc.
    reference_id = Column(Integer, nullable=True)
    reference_number = Column(String(100), nullable=True)
    
    # Description
    description = Column(Text, nullable=True)
    
    # Balances after movement
    quantity_before = Column(Numeric(15, 3), nullable=False)
    quantity_after = Column(Numeric(15, 3), nullable=False)
    
    # User who created the movement
    created_by_id = Column(Integer, ForeignKey("users.id"), nullable=False)
    
    # Company association
    company_id = Column(Integer, ForeignKey("companies.id"), nullable=False)
    
    # Relationships
    company = relationship("Company")
    inventory_item = relationship("InventoryItem", back_populates="movements")
    created_by = relationship("User")
    
    @property
    def total_value(self) -> Decimal:
        """Calculate total value of the movement."""
        return abs(self.quantity) * self.unit_cost
    
    @classmethod
    def create_movement(
        cls,
        inventory_item: InventoryItem,
        movement_type: MovementType,
        quantity: Decimal,
        unit_cost: Decimal = Decimal('0'),
        description: str = None,
        reference_type: str = None,
        reference_id: int = None,
        reference_number: str = None,
        created_by_id: int = None,
        company_id: int = None
    ) -> 'InventoryMovement':
        """Create a new inventory movement and update stock levels."""
        
        # Record quantities before movement
        quantity_before = inventory_item.quantity_on_hand
        
        # Create movement record
        movement = cls(
            inventory_item_id=inventory_item.id,
            movement_type=movement_type,
            quantity=quantity,
            unit_cost=unit_cost,
            description=description,
            reference_type=reference_type,
            reference_id=reference_id,
            reference_number=reference_number,
            quantity_before=quantity_before,
            created_by_id=created_by_id,
            company_id=company_id
        )
        
        # Update inventory quantities
        if movement_type == MovementType.IN:
            inventory_item.quantity_on_hand += quantity
            if unit_cost > 0:
                inventory_item.update_average_cost(quantity, unit_cost)
        elif movement_type == MovementType.OUT:
            inventory_item.quantity_on_hand -= quantity
        elif movement_type == MovementType.ADJUSTMENT:
            inventory_item.quantity_on_hand = quantity
        
        # Record quantity after movement
        movement.quantity_after = inventory_item.quantity_on_hand
        
        # Update calculated quantities
        inventory_item.update_quantities()
        
        return movement
