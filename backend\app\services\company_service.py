"""
Company service for company-related operations.
"""

from typing import Optional, List
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select
from datetime import date

from app.models.company import Company, CompanySettings
from app.schemas.company import CompanyCreate, CompanyUpdate
from app.services.base_service import BaseService


class CompanyService(BaseService[Company, CompanyCreate, CompanyUpdate]):
    """Company service class."""
    
    def __init__(self, db: AsyncSession):
        super().__init__(Company, db)
    
    async def get_by_name(self, name: str) -> Optional[Company]:
        """Get company by name."""
        result = await self.db.execute(
            select(Company).where(
                Company.name == name,
                Company.is_deleted == False
            )
        )
        return result.scalar_one_or_none()
    
    async def search_companies(self, query: str) -> List[Company]:
        """Search companies by name or legal name."""
        search_filter = select(Company).where(
            Company.is_deleted == False
        ).where(
            (Company.name.ilike(f"%{query}%")) |
            (Company.legal_name.ilike(f"%{query}%"))
        )
        
        result = await self.db.execute(search_filter.order_by(Company.name))
        return result.scalars().all()
    
    async def get_settings(self, company_id: int) -> Optional[CompanySettings]:
        """Get company settings."""
        result = await self.db.execute(
            select(CompanySettings).where(
                CompanySettings.company_id == company_id,
                CompanySettings.is_deleted == False
            )
        )
        return result.scalar_one_or_none()
    
    async def create_default_settings(self, company_id: int) -> CompanySettings:
        """Create default settings for a company."""
        settings = CompanySettings(
            company_id=company_id,
            chart_of_accounts_template="standard",
            enable_multi_currency=False,
            auto_create_accounts=True,
            invoice_prefix="INV",
            invoice_number_format="{prefix}-{year}-{number:04d}",
            next_invoice_number=1,
            default_tax_rate="0.00",
            tax_inclusive_pricing=False,
            enable_inventory=True,
            inventory_valuation_method="FIFO",
            low_stock_threshold=10,
            email_notifications=True,
            low_stock_alerts=True,
            payment_reminders=True,
            default_date_format="YYYY-MM-DD",
            default_number_format="#,##0.00"
        )
        
        self.db.add(settings)
        await self.db.flush()
        await self.db.refresh(settings)
        return settings
    
    async def update_settings(self, company_id: int, settings_data: dict) -> Optional[CompanySettings]:
        """Update company settings."""
        settings = await self.get_settings(company_id)
        if not settings:
            return None
        
        for key, value in settings_data.items():
            if hasattr(settings, key):
                setattr(settings, key, value)
        
        await self.db.flush()
        await self.db.refresh(settings)
        return settings
    
    async def initialize_chart_of_accounts(self, company_id: int, template: str = "standard") -> bool:
        """Initialize chart of accounts for a company."""
        from app.services.account_service import AccountService
        from app.models.account import AccountType
        
        account_service = AccountService(self.db)
        
        # Standard chart of accounts template
        if template == "standard":
            accounts = [
                # Assets
                {"code": "1000", "name": "Cash", "account_type": AccountType.ASSET, "company_id": company_id},
                {"code": "1100", "name": "Accounts Receivable", "account_type": AccountType.ASSET, "company_id": company_id},
                {"code": "1200", "name": "Inventory", "account_type": AccountType.ASSET, "company_id": company_id},
                {"code": "1500", "name": "Equipment", "account_type": AccountType.ASSET, "company_id": company_id},
                
                # Liabilities
                {"code": "2000", "name": "Accounts Payable", "account_type": AccountType.LIABILITY, "company_id": company_id},
                {"code": "2100", "name": "Accrued Expenses", "account_type": AccountType.LIABILITY, "company_id": company_id},
                {"code": "2500", "name": "Long-term Debt", "account_type": AccountType.LIABILITY, "company_id": company_id},
                
                # Equity
                {"code": "3000", "name": "Owner's Equity", "account_type": AccountType.EQUITY, "company_id": company_id},
                {"code": "3100", "name": "Retained Earnings", "account_type": AccountType.EQUITY, "company_id": company_id},
                
                # Revenue
                {"code": "4000", "name": "Sales Revenue", "account_type": AccountType.REVENUE, "company_id": company_id},
                {"code": "4100", "name": "Service Revenue", "account_type": AccountType.REVENUE, "company_id": company_id},
                
                # Expenses
                {"code": "5000", "name": "Cost of Goods Sold", "account_type": AccountType.EXPENSE, "company_id": company_id},
                {"code": "6000", "name": "Operating Expenses", "account_type": AccountType.EXPENSE, "company_id": company_id},
                {"code": "6100", "name": "Salaries and Wages", "account_type": AccountType.EXPENSE, "company_id": company_id},
                {"code": "6200", "name": "Rent Expense", "account_type": AccountType.EXPENSE, "company_id": company_id},
                {"code": "6300", "name": "Utilities Expense", "account_type": AccountType.EXPENSE, "company_id": company_id},
            ]
            
            # Create accounts
            for account_data in accounts:
                await account_service.create(account_data)
            
            return True
        
        return False
