"""
سكريبت رفع النظام للإنترنت
Upload system to web script
"""

import os
import requests
import json
from pathlib import Path
import base64

def upload_to_file_io():
    """رفع الملف على file.io (خدمة مجانية)"""
    print("🌐 رفع النظام على file.io...")
    
    zip_file = None
    for file in Path('.').glob('GlobalAccountingSystem_v*.zip'):
        zip_file = file
        break
    
    if not zip_file:
        print("❌ لم يتم العثور على ملف ZIP")
        return None
    
    try:
        with open(zip_file, 'rb') as f:
            files = {'file': f}
            response = requests.post('https://file.io', files=files)
        
        if response.status_code == 200:
            data = response.json()
            if data.get('success'):
                download_url = data.get('link')
                print(f"✅ تم الرفع بنجاح!")
                print(f"🔗 رابط التحميل: {download_url}")
                return download_url
            else:
                print("❌ فشل في الرفع")
                return None
        else:
            print(f"❌ خطأ في الرفع: {response.status_code}")
            return None
            
    except Exception as e:
        print(f"❌ خطأ: {e}")
        return None

def upload_to_transfer_sh():
    """رفع الملف على transfer.sh (خدمة مجانية)"""
    print("🌐 رفع النظام على transfer.sh...")
    
    zip_file = None
    for file in Path('.').glob('GlobalAccountingSystem_v*.zip'):
        zip_file = file
        break
    
    if not zip_file:
        print("❌ لم يتم العثور على ملف ZIP")
        return None
    
    try:
        with open(zip_file, 'rb') as f:
            files = {'file': f}
            response = requests.post(
                f'https://transfer.sh/{zip_file.name}',
                data=f.read(),
                headers={'Content-Type': 'application/octet-stream'}
            )
        
        if response.status_code == 200:
            download_url = response.text.strip()
            print(f"✅ تم الرفع بنجاح!")
            print(f"🔗 رابط التحميل: {download_url}")
            return download_url
        else:
            print(f"❌ خطأ في الرفع: {response.status_code}")
            return None
            
    except Exception as e:
        print(f"❌ خطأ: {e}")
        return None

def create_download_page(download_urls):
    """إنشاء صفحة تحميل HTML"""
    html_content = f'''<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تحميل نظام المحاسبة العالمي</title>
    <style>
        body {{
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            margin: 0;
            padding: 20px;
            min-height: 100vh;
            direction: rtl;
        }}
        .container {{
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }}
        .header {{
            background: linear-gradient(45deg, #4CAF50, #45a049);
            color: white;
            padding: 40px;
            text-align: center;
        }}
        .header h1 {{
            margin: 0;
            font-size: 2.5em;
            margin-bottom: 10px;
        }}
        .header p {{
            margin: 0;
            font-size: 1.2em;
            opacity: 0.9;
        }}
        .content {{
            padding: 40px;
        }}
        .download-section {{
            background: #f8f9fa;
            border-radius: 10px;
            padding: 30px;
            margin: 20px 0;
            text-align: center;
        }}
        .download-btn {{
            display: inline-block;
            background: linear-gradient(45deg, #FF6B6B, #FF8E53);
            color: white;
            padding: 15px 30px;
            text-decoration: none;
            border-radius: 50px;
            font-size: 1.2em;
            font-weight: bold;
            margin: 10px;
            transition: transform 0.3s;
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }}
        .download-btn:hover {{
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.3);
        }}
        .features {{
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin: 30px 0;
        }}
        .feature {{
            background: #f8f9fa;
            padding: 20px;
            border-radius: 10px;
            text-align: center;
        }}
        .feature-icon {{
            font-size: 3em;
            margin-bottom: 15px;
        }}
        .info-box {{
            background: #e3f2fd;
            border-right: 5px solid #2196F3;
            padding: 20px;
            margin: 20px 0;
            border-radius: 5px;
        }}
        .warning-box {{
            background: #fff3e0;
            border-right: 5px solid #ff9800;
            padding: 20px;
            margin: 20px 0;
            border-radius: 5px;
        }}
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🌍 نظام المحاسبة العالمي</h1>
            <p>Global Accounting System</p>
            <p>الإصدار 1.0.0 - مجاني بالكامل</p>
        </div>
        
        <div class="content">
            <div class="download-section">
                <h2>📥 تحميل النظام</h2>
                <p>حجم الملف: 13.9 MB | متوافق مع جميع إصدارات Windows</p>
'''

    # إضافة روابط التحميل
    for i, url in enumerate(download_urls, 1):
        if url:
            html_content += f'''
                <a href="{url}" class="download-btn">
                    📥 تحميل من الخادم {i}
                </a>
'''

    html_content += '''
            </div>
            
            <div class="info-box">
                <h3>🔑 بيانات تسجيل الدخول</h3>
                <p><strong>اسم المستخدم:</strong> admin</p>
                <p><strong>كلمة المرور:</strong> admin123</p>
            </div>
            
            <div class="warning-box">
                <h3>⚠️ تعليمات مهمة</h3>
                <ol>
                    <li>فك الضغط عن الملف المحمل</li>
                    <li>تشغيل ملف "GlobalAccountingSystem.exe"</li>
                    <li>انتظار تحميل النظام (قد يستغرق دقيقة)</li>
                    <li>فتح المتصفح والذهاب إلى: http://localhost:8000</li>
                    <li>تسجيل الدخول بالبيانات المذكورة أعلاه</li>
                </ol>
            </div>
            
            <div class="features">
                <div class="feature">
                    <div class="feature-icon">🔐</div>
                    <h3>نظام مصادقة آمن</h3>
                    <p>حماية متقدمة للبيانات</p>
                </div>
                <div class="feature">
                    <div class="feature-icon">📊</div>
                    <h3>لوحة تحكم تفاعلية</h3>
                    <p>إحصائيات ومؤشرات فورية</p>
                </div>
                <div class="feature">
                    <div class="feature-icon">🏢</div>
                    <h3>إدارة الشركات</h3>
                    <p>نظام CRM متكامل</p>
                </div>
                <div class="feature">
                    <div class="feature-icon">💰</div>
                    <h3>دليل الحسابات</h3>
                    <p>نظام محاسبي شامل</p>
                </div>
            </div>
            
            <div class="info-box">
                <h3>📱 الميزات المتاحة</h3>
                <ul>
                    <li>✅ نظام محاسبة متكامل</li>
                    <li>✅ إدارة العملاء والموردين</li>
                    <li>✅ نظام الفواتير</li>
                    <li>✅ التقارير المالية</li>
                    <li>✅ دعم العملات المتعددة</li>
                    <li>✅ واجهة عربية/إنجليزية</li>
                    <li>✅ لا يحتاج اتصال إنترنت</li>
                    <li>✅ يعمل بدون تثبيت Python</li>
                </ul>
            </div>
            
            <div class="download-section">
                <h3>🆘 الدعم الفني</h3>
                <p>للمساعدة والاستفسارات:</p>
                <p><strong>البريد الإلكتروني:</strong> <EMAIL></p>
                <p><strong>الوثائق:</strong> متاحة داخل النظام</p>
            </div>
        </div>
    </div>
    
    <script>
        // إحصائيات التحميل
        let downloadCount = localStorage.getItem('downloadCount') || 0;
        downloadCount++;
        localStorage.setItem('downloadCount', downloadCount);
        
        // عرض رسالة ترحيب
        setTimeout(() => {{
            alert('مرحباً بك في نظام المحاسبة العالمي!\\n\\nبعد التحميل:\\n1. فك الضغط\\n2. تشغيل GlobalAccountingSystem.exe\\n3. فتح http://localhost:8000\\n4. تسجيل الدخول: admin / admin123');
        }}, 1000);
    </script>
</body>
</html>'''

    # حفظ صفحة التحميل
    download_page = Path('download_page.html')
    with open(download_page, 'w', encoding='utf-8') as f:
        f.write(html_content)
    
    print(f"✅ تم إنشاء صفحة التحميل: {download_page.absolute()}")
    return str(download_page.absolute())

def main():
    """الدالة الرئيسية"""
    print("🚀 نشر نظام المحاسبة العالمي على الإنترنت")
    print("=" * 60)
    
    download_urls = []
    
    # رفع على خدمات مختلفة
    print("📤 رفع الملفات...")
    
    # file.io
    url1 = upload_to_file_io()
    if url1:
        download_urls.append(url1)
    
    # transfer.sh
    url2 = upload_to_transfer_sh()
    if url2:
        download_urls.append(url2)
    
    if download_urls:
        # إنشاء صفحة التحميل
        page_path = create_download_page(download_urls)
        
        print("\n" + "=" * 60)
        print("🎉 تم نشر النظام بنجاح!")
        print("=" * 60)
        print("🔗 روابط التحميل:")
        for i, url in enumerate(download_urls, 1):
            print(f"   {i}. {url}")
        
        print(f"\n📄 صفحة التحميل: {page_path}")
        print("\n📋 معلومات النشر:")
        print("- حجم الملف: 13.9 MB")
        print("- متوافق مع: Windows 7/8/10/11")
        print("- لا يحتاج تثبيت")
        print("- يعمل مباشرة")
        
        print("\n🎊 النظام متاح الآن للتحميل من الإنترنت!")
    else:
        print("❌ فشل في رفع الملفات")

if __name__ == "__main__":
    main()
