"""
Company model and related classes.
"""

from sqlalchemy import Column, Integer, String, Text, Boolean, JSON, Date
from sqlalchemy.orm import relationship
from datetime import date

from .base import BaseModel


class Company(BaseModel):
    """Company model."""
    
    __tablename__ = "companies"
    
    # Basic information
    name = Column(String(255), nullable=False, index=True)
    legal_name = Column(String(255), nullable=True)
    registration_number = Column(String(100), nullable=True)
    tax_id = Column(String(100), nullable=True)
    
    # Contact information
    email = Column(String(255), nullable=True)
    phone = Column(String(20), nullable=True)
    website = Column(String(255), nullable=True)
    
    # Address
    address_line1 = Column(String(255), nullable=True)
    address_line2 = Column(String(255), nullable=True)
    city = Column(String(100), nullable=True)
    state = Column(String(100), nullable=True)
    postal_code = Column(String(20), nullable=True)
    country = Column(String(100), nullable=True)
    
    # Business information
    industry = Column(String(100), nullable=True)
    description = Column(Text, nullable=True)
    logo_url = Column(String(500), nullable=True)
    
    # Settings
    default_currency = Column(String(3), default="USD", nullable=False)
    fiscal_year_start = Column(Date, nullable=True)
    timezone = Column(String(50), default="UTC", nullable=False)
    
    # Status
    is_active = Column(Boolean, default=True, nullable=False)
    
    # Relationships
    users = relationship("User", back_populates="company")
    accounts = relationship("Account", back_populates="company")
    customers = relationship("Customer", back_populates="company")
    suppliers = relationship("Supplier", back_populates="company")
    products = relationship("Product", back_populates="company")
    invoices = relationship("Invoice", back_populates="company")
    transactions = relationship("Transaction", back_populates="company")
    settings = relationship("CompanySettings", back_populates="company", uselist=False)
    
    @property
    def full_address(self) -> str:
        """Get formatted full address."""
        address_parts = [
            self.address_line1,
            self.address_line2,
            self.city,
            self.state,
            self.postal_code,
            self.country
        ]
        return ", ".join(filter(None, address_parts))


class CompanySettings(BaseModel):
    """Company-specific settings."""
    
    __tablename__ = "company_settings"
    
    company_id = Column(Integer, ForeignKey("companies.id"), nullable=False, unique=True)
    
    # Accounting settings
    chart_of_accounts_template = Column(String(50), default="standard", nullable=False)
    enable_multi_currency = Column(Boolean, default=False, nullable=False)
    auto_create_accounts = Column(Boolean, default=True, nullable=False)
    
    # Invoice settings
    invoice_prefix = Column(String(10), default="INV", nullable=False)
    invoice_number_format = Column(String(50), default="{prefix}-{year}-{number:04d}", nullable=False)
    next_invoice_number = Column(Integer, default=1, nullable=False)
    
    # Tax settings
    default_tax_rate = Column(String(10), default="0.00", nullable=False)
    tax_inclusive_pricing = Column(Boolean, default=False, nullable=False)
    
    # Inventory settings
    enable_inventory = Column(Boolean, default=True, nullable=False)
    inventory_valuation_method = Column(String(20), default="FIFO", nullable=False)
    low_stock_threshold = Column(Integer, default=10, nullable=False)
    
    # Notification settings
    email_notifications = Column(Boolean, default=True, nullable=False)
    low_stock_alerts = Column(Boolean, default=True, nullable=False)
    payment_reminders = Column(Boolean, default=True, nullable=False)
    
    # Report settings
    default_date_format = Column(String(20), default="YYYY-MM-DD", nullable=False)
    default_number_format = Column(String(20), default="#,##0.00", nullable=False)
    
    # Custom fields and settings
    custom_settings = Column(JSON, nullable=True)
    
    # Relationships
    company = relationship("Company", back_populates="settings")
