"""
Authentication schemas.
"""

from pydantic import BaseModel, EmailStr, validator
from typing import Optional
from app.models.user import UserRole


class Token(BaseModel):
    """Token response schema."""
    access_token: str
    refresh_token: str
    token_type: str = "bearer"
    expires_in: int


class UserCreate(BaseModel):
    """User creation schema."""
    email: EmailStr
    username: str
    first_name: str
    last_name: str
    password: str
    phone: Optional[str] = None
    company_id: Optional[int] = None
    
    @validator('username')
    def validate_username(cls, v):
        if len(v) < 3:
            raise ValueError('Username must be at least 3 characters long')
        if not v.isalnum():
            raise ValueError('Username must contain only letters and numbers')
        return v
    
    @validator('password')
    def validate_password(cls, v):
        if len(v) < 8:
            raise ValueError('Password must be at least 8 characters long')
        return v


class UserResponse(BaseModel):
    """User response schema."""
    id: int
    email: str
    username: str
    first_name: str
    last_name: str
    full_name: str
    phone: Optional[str]
    avatar_url: Optional[str]
    bio: Optional[str]
    role: UserRole
    is_active: bool
    is_verified: bool
    company_id: Optional[int]
    
    class Config:
        from_attributes = True
