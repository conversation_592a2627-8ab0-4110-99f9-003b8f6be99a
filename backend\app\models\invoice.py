"""
Invoice and InvoiceItem models for billing.
"""

from sqlalchemy import Column, Integer, String, Text, Date, ForeignKey, Numeric, <PERSON><PERSON>an, Enum
from sqlalchemy.orm import relationship
from enum import Enum as PyEnum
from decimal import Decimal
from datetime import date, timedelta

from .base import BaseModel


class InvoiceType(PyEnum):
    """Invoice types."""
    SALES = "sales"
    PURCHASE = "purchase"


class InvoiceStatus(PyEnum):
    """Invoice status."""
    DRAFT = "draft"
    SENT = "sent"
    PAID = "paid"
    OVERDUE = "overdue"
    CANCELLED = "cancelled"
    RECEIVED = "received"  # For purchase invoices


class Invoice(BaseModel):
    """Invoice model."""
    
    __tablename__ = "invoices"
    
    # Basic information
    invoice_number = Column(String(100), nullable=False, index=True)
    invoice_type = Column(Enum(InvoiceType), nullable=False)
    status = Column(Enum(InvoiceStatus), default=InvoiceStatus.DRAFT, nullable=False)
    
    # Dates
    invoice_date = Column(Date, nullable=False, index=True)
    due_date = Column(Date, nullable=False, index=True)
    
    # Customer/Supplier
    customer_id = Column(Integer, ForeignKey("customers.id"), nullable=True)
    supplier_id = Column(Integer, ForeignKey("suppliers.id"), nullable=True)
    
    # Amounts
    subtotal = Column(Numeric(15, 2), default=0, nullable=False)
    tax_amount = Column(Numeric(15, 2), default=0, nullable=False)
    discount_amount = Column(Numeric(15, 2), default=0, nullable=False)
    total_amount = Column(Numeric(15, 2), default=0, nullable=False)
    paid_amount = Column(Numeric(15, 2), default=0, nullable=False)
    
    # Currency
    currency = Column(String(3), default="USD", nullable=False)
    exchange_rate = Column(Numeric(10, 6), default=1, nullable=False)
    
    # Notes and terms
    notes = Column(Text, nullable=True)
    terms_and_conditions = Column(Text, nullable=True)
    
    # References
    reference_number = Column(String(100), nullable=True)
    purchase_order_number = Column(String(100), nullable=True)
    
    # Company association
    company_id = Column(Integer, ForeignKey("companies.id"), nullable=False)
    
    # User who created the invoice
    created_by_id = Column(Integer, ForeignKey("users.id"), nullable=False)
    
    # Relationships
    company = relationship("Company", back_populates="invoices")
    customer = relationship("Customer", back_populates="invoices")
    supplier = relationship("Supplier", back_populates="purchase_invoices")
    created_by = relationship("User")
    items = relationship("InvoiceItem", back_populates="invoice", cascade="all, delete-orphan")
    
    @property
    def outstanding_amount(self) -> Decimal:
        """Calculate outstanding amount."""
        return self.total_amount - self.paid_amount
    
    @property
    def is_fully_paid(self) -> bool:
        """Check if invoice is fully paid."""
        return self.paid_amount >= self.total_amount
    
    @property
    def is_overdue(self) -> bool:
        """Check if invoice is overdue."""
        if self.status in [InvoiceStatus.PAID, InvoiceStatus.CANCELLED]:
            return False
        return date.today() > self.due_date
    
    @property
    def days_overdue(self) -> int:
        """Calculate days overdue."""
        if not self.is_overdue:
            return 0
        return (date.today() - self.due_date).days
    
    def calculate_totals(self) -> None:
        """Calculate invoice totals from items."""
        self.subtotal = sum(item.total_amount for item in self.items)
        self.tax_amount = sum(item.tax_amount for item in self.items)
        self.total_amount = self.subtotal + self.tax_amount - self.discount_amount
    
    def add_payment(self, amount: Decimal) -> None:
        """Add payment to invoice."""
        self.paid_amount += amount
        if self.is_fully_paid:
            self.status = InvoiceStatus.PAID
    
    def send(self) -> None:
        """Mark invoice as sent."""
        if self.status == InvoiceStatus.DRAFT:
            self.status = InvoiceStatus.SENT
    
    def cancel(self) -> None:
        """Cancel the invoice."""
        if self.status not in [InvoiceStatus.PAID]:
            self.status = InvoiceStatus.CANCELLED


class InvoiceItem(BaseModel):
    """Invoice item model."""
    
    __tablename__ = "invoice_items"
    
    # Invoice reference
    invoice_id = Column(Integer, ForeignKey("invoices.id"), nullable=False)
    
    # Product reference (optional - can be manual item)
    product_id = Column(Integer, ForeignKey("products.id"), nullable=True)
    
    # Item details
    description = Column(Text, nullable=False)
    quantity = Column(Numeric(15, 3), nullable=False)
    unit_price = Column(Numeric(15, 2), nullable=False)
    
    # Tax
    tax_rate = Column(Numeric(5, 2), default=0, nullable=False)
    tax_amount = Column(Numeric(15, 2), default=0, nullable=False)
    
    # Discount
    discount_rate = Column(Numeric(5, 2), default=0, nullable=False)
    discount_amount = Column(Numeric(15, 2), default=0, nullable=False)
    
    # Total
    total_amount = Column(Numeric(15, 2), nullable=False)
    
    # Relationships
    invoice = relationship("Invoice", back_populates="items")
    product = relationship("Product", back_populates="invoice_items")
    
    def calculate_amounts(self) -> None:
        """Calculate item amounts."""
        line_total = self.quantity * self.unit_price
        
        # Apply discount
        if self.discount_rate > 0:
            self.discount_amount = line_total * (self.discount_rate / 100)
        else:
            self.discount_amount = self.discount_amount or Decimal('0')
        
        subtotal = line_total - self.discount_amount
        
        # Calculate tax
        if self.tax_rate > 0:
            self.tax_amount = subtotal * (self.tax_rate / 100)
        else:
            self.tax_amount = Decimal('0')
        
        self.total_amount = subtotal + self.tax_amount
