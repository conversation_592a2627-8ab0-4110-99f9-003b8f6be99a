"""
Transactions endpoints.
"""

from typing import List, Optional
from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.ext.asyncio import AsyncSession
from datetime import date

from app.core.database import get_db
from app.core.security import get_current_active_user, require_permissions
from app.models.user import User
from app.services.transaction_service import TransactionService
from app.schemas.transaction import (
    TransactionResponse, TransactionCreate, TransactionUpdate, TransactionList
)

router = APIRouter()


@router.get("/", response_model=TransactionList)
async def get_transactions(
    skip: int = Query(0, ge=0),
    limit: int = Query(20, ge=1, le=100),
    search: Optional[str] = Query(None),
    start_date: Optional[date] = Query(None),
    end_date: Optional[date] = Query(None),
    posted_only: bool = Query(False),
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """Get list of transactions with pagination and filtering."""
    transaction_service = TransactionService(db)
    
    # Build filters
    filters = {"company_id": current_user.company_id}
    if posted_only:
        filters["is_posted"] = True
    
    # Get transactions
    transactions = await transaction_service.get_transactions_filtered(
        company_id=current_user.company_id,
        skip=skip,
        limit=limit,
        search=search,
        start_date=start_date,
        end_date=end_date,
        posted_only=posted_only
    )
    
    total = await transaction_service.count_transactions_filtered(
        company_id=current_user.company_id,
        search=search,
        start_date=start_date,
        end_date=end_date,
        posted_only=posted_only
    )
    
    return TransactionList(
        transactions=transactions,
        total=total,
        page=skip // limit + 1,
        size=limit,
        pages=(total + limit - 1) // limit
    )


@router.get("/{transaction_id}", response_model=TransactionResponse)
async def get_transaction(
    transaction_id: int,
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """Get transaction by ID."""
    transaction_service = TransactionService(db)
    transaction = await transaction_service.get_by_id(transaction_id)
    
    if not transaction:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Transaction not found"
        )
    
    # Check if transaction belongs to user's company
    if transaction.company_id != current_user.company_id:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Not enough permissions"
        )
    
    return transaction


@router.post("/", response_model=TransactionResponse)
@require_permissions("manage_transactions")
async def create_transaction(
    transaction_data: TransactionCreate,
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """Create a new transaction."""
    transaction_service = TransactionService(db)
    
    # Set company_id and created_by_id
    transaction_dict = transaction_data.dict()
    transaction_dict["company_id"] = current_user.company_id
    transaction_dict["created_by_id"] = current_user.id
    
    # Validate transaction entries
    if not transaction_data.entries or len(transaction_data.entries) < 2:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Transaction must have at least 2 entries"
        )
    
    # Check if transaction is balanced
    total_debits = sum(entry.debit_amount for entry in transaction_data.entries)
    total_credits = sum(entry.credit_amount for entry in transaction_data.entries)
    
    if total_debits != total_credits:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Transaction is not balanced. Debits must equal credits."
        )
    
    # Create transaction
    transaction = await transaction_service.create_transaction(transaction_dict)
    return transaction


@router.put("/{transaction_id}", response_model=TransactionResponse)
@require_permissions("manage_transactions")
async def update_transaction(
    transaction_id: int,
    transaction_data: TransactionUpdate,
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """Update transaction."""
    transaction_service = TransactionService(db)
    transaction = await transaction_service.get_by_id(transaction_id)
    
    if not transaction:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Transaction not found"
        )
    
    # Check if transaction belongs to user's company
    if transaction.company_id != current_user.company_id:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Not enough permissions"
        )
    
    # Check if transaction is already posted
    if transaction.is_posted:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Cannot modify posted transaction"
        )
    
    # Update transaction
    updated_transaction = await transaction_service.update_transaction(
        transaction_id, transaction_data.dict(exclude_unset=True)
    )
    return updated_transaction


@router.delete("/{transaction_id}")
@require_permissions("manage_transactions")
async def delete_transaction(
    transaction_id: int,
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """Delete transaction."""
    transaction_service = TransactionService(db)
    transaction = await transaction_service.get_by_id(transaction_id)
    
    if not transaction:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Transaction not found"
        )
    
    # Check if transaction belongs to user's company
    if transaction.company_id != current_user.company_id:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Not enough permissions"
        )
    
    # Check if transaction is already posted
    if transaction.is_posted:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Cannot delete posted transaction"
        )
    
    success = await transaction_service.delete(transaction_id)
    if not success:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Failed to delete transaction"
        )
    
    return {"message": "Transaction deleted successfully"}


@router.post("/{transaction_id}/post")
@require_permissions("manage_transactions")
async def post_transaction(
    transaction_id: int,
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """Post transaction to update account balances."""
    transaction_service = TransactionService(db)
    transaction = await transaction_service.get_by_id(transaction_id)
    
    if not transaction:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Transaction not found"
        )
    
    # Check if transaction belongs to user's company
    if transaction.company_id != current_user.company_id:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Not enough permissions"
        )
    
    # Check if transaction is already posted
    if transaction.is_posted:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Transaction is already posted"
        )
    
    # Post transaction
    success = await transaction_service.post_transaction(transaction_id)
    if not success:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Failed to post transaction"
        )
    
    return {"message": "Transaction posted successfully"}


@router.post("/{transaction_id}/reverse")
@require_permissions("manage_transactions")
async def reverse_transaction(
    transaction_id: int,
    reversal_date: date,
    description: Optional[str] = None,
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """Reverse a posted transaction."""
    transaction_service = TransactionService(db)
    transaction = await transaction_service.get_by_id(transaction_id)
    
    if not transaction:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Transaction not found"
        )
    
    # Check if transaction belongs to user's company
    if transaction.company_id != current_user.company_id:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Not enough permissions"
        )
    
    # Check if transaction is posted
    if not transaction.is_posted:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Cannot reverse unposted transaction"
        )
    
    # Check if transaction is already reversed
    if transaction.is_reversed:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Transaction is already reversed"
        )
    
    # Reverse transaction
    reversal_transaction = await transaction_service.reverse_transaction(
        transaction_id, reversal_date, description, current_user.id
    )
    
    return {
        "message": "Transaction reversed successfully",
        "reversal_transaction_id": reversal_transaction.id
    }


@router.get("/journal/entries")
async def get_journal_entries(
    skip: int = Query(0, ge=0),
    limit: int = Query(50, ge=1, le=200),
    start_date: Optional[date] = Query(None),
    end_date: Optional[date] = Query(None),
    account_id: Optional[int] = Query(None),
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """Get journal entries (general ledger)."""
    transaction_service = TransactionService(db)
    
    entries = await transaction_service.get_journal_entries(
        company_id=current_user.company_id,
        skip=skip,
        limit=limit,
        start_date=start_date,
        end_date=end_date,
        account_id=account_id
    )
    
    return {"entries": entries}
