import { render, screen } from '@testing-library/react'
import HomePage from '@/app/page'

// Mock next/navigation
jest.mock('next/navigation', () => ({
  useRouter: () => ({
    push: jest.fn(),
  }),
}))

// Mock localStorage
Object.defineProperty(window, 'localStorage', {
  value: {
    getItem: jest.fn(),
    setItem: jest.fn(),
    removeItem: jest.fn(),
  },
  writable: true,
})

describe('HomePage', () => {
  it('renders the main heading', () => {
    render(<HomePage />)
    
    const heading = screen.getByText('Modern Accounting')
    expect(heading).toBeInTheDocument()
  })

  it('renders the subtitle', () => {
    render(<HomePage />)
    
    const subtitle = screen.getByText('Made Simple')
    expect(subtitle).toBeInTheDocument()
  })

  it('renders the get started button', () => {
    render(<HomePage />)
    
    const button = screen.getByText('Start Free Trial')
    expect(button).toBeInTheDocument()
  })

  it('renders feature sections', () => {
    render(<HomePage />)
    
    expect(screen.getByText('Complete Accounting')).toBeInTheDocument()
    expect(screen.getByText('Invoice Management')).toBeInTheDocument()
    expect(screen.getByText('Customer & Supplier CRM')).toBeInTheDocument()
  })
})
