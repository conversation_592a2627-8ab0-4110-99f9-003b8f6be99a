{"buildCommand": "npm run build", "outputDirectory": ".next", "framework": "nextjs", "installCommand": "npm install", "devCommand": "npm run dev", "env": {"NEXT_PUBLIC_API_URL": "https://global-accounting-backend.railway.app", "NEXT_PUBLIC_APP_NAME": "نظام المحاسبة العالمي"}, "build": {"env": {"NEXT_PUBLIC_API_URL": "https://global-accounting-backend.railway.app", "NEXT_PUBLIC_APP_NAME": "نظام المحاسبة العالمي"}}, "functions": {"app/**/*.tsx": {"maxDuration": 30}}, "headers": [{"source": "/api/(.*)", "headers": [{"key": "Access-Control-Allow-Origin", "value": "*"}, {"key": "Access-Control-Allow-Methods", "value": "GET, POST, PUT, DELETE, OPTIONS"}, {"key": "Access-Control-Allow-Headers", "value": "Content-Type, Authorization"}]}], "rewrites": [{"source": "/api/:path*", "destination": "https://global-accounting-backend.railway.app/api/:path*"}]}