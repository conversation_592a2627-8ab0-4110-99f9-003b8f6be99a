#!/bin/bash

# Global Accounting System Setup Script
# This script sets up the development environment

set -e

echo "🚀 Setting up Global Accounting System..."

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if required tools are installed
check_requirements() {
    print_status "Checking requirements..."
    
    # Check Python
    if ! command -v python3 &> /dev/null; then
        print_error "Python 3 is not installed. Please install Python 3.9 or higher."
        exit 1
    fi
    
    # Check Node.js
    if ! command -v node &> /dev/null; then
        print_error "Node.js is not installed. Please install Node.js 18 or higher."
        exit 1
    fi
    
    # Check npm
    if ! command -v npm &> /dev/null; then
        print_error "npm is not installed. Please install npm."
        exit 1
    fi
    
    # Check Docker (optional)
    if ! command -v docker &> /dev/null; then
        print_warning "Docker is not installed. You can still run the application without Docker."
    fi
    
    print_success "All requirements are met!"
}

# Setup backend
setup_backend() {
    print_status "Setting up backend..."
    
    cd backend
    
    # Create virtual environment
    if [ ! -d "venv" ]; then
        print_status "Creating Python virtual environment..."
        python3 -m venv venv
    fi
    
    # Activate virtual environment
    source venv/bin/activate || source venv/Scripts/activate
    
    # Install dependencies
    print_status "Installing Python dependencies..."
    pip install --upgrade pip
    pip install -r requirements.txt
    
    # Copy environment file
    if [ ! -f ".env" ]; then
        print_status "Creating backend environment file..."
        cp .env.example .env
        print_warning "Please update the .env file with your configuration!"
    fi
    
    cd ..
    print_success "Backend setup completed!"
}

# Setup frontend
setup_frontend() {
    print_status "Setting up frontend..."
    
    cd frontend
    
    # Install dependencies
    print_status "Installing Node.js dependencies..."
    npm install
    
    # Copy environment file
    if [ ! -f ".env.local" ]; then
        print_status "Creating frontend environment file..."
        cp .env.example .env.local
        print_warning "Please update the .env.local file with your configuration!"
    fi
    
    cd ..
    print_success "Frontend setup completed!"
}

# Setup database
setup_database() {
    print_status "Setting up database..."
    
    # Check if PostgreSQL is running
    if command -v psql &> /dev/null; then
        print_status "PostgreSQL found. Creating database..."
        
        # Try to create database
        createdb accounting_db 2>/dev/null || print_warning "Database might already exist or PostgreSQL is not running"
        
        print_success "Database setup completed!"
    else
        print_warning "PostgreSQL not found. Please install PostgreSQL or use Docker."
        print_status "You can use Docker to run PostgreSQL:"
        echo "  docker run -d --name postgres -e POSTGRES_PASSWORD=password -e POSTGRES_DB=accounting_db -p 5432:5432 postgres:15"
    fi
}

# Setup with Docker
setup_docker() {
    print_status "Setting up with Docker..."
    
    if command -v docker-compose &> /dev/null || command -v docker &> /dev/null; then
        print_status "Building Docker containers..."
        docker-compose build
        
        print_status "Starting services..."
        docker-compose up -d postgres redis
        
        print_success "Docker setup completed!"
        print_status "You can start all services with: docker-compose up"
    else
        print_error "Docker is not installed. Please install Docker to use this option."
        exit 1
    fi
}

# Create initial admin user
create_admin_user() {
    print_status "Creating initial admin user..."
    
    cd backend
    source venv/bin/activate || source venv/Scripts/activate
    
    # Run the admin user creation script
    python -c "
import asyncio
from app.core.database import AsyncSessionLocal
from app.services.user_service import UserService
from app.core.security import get_password_hash
from app.models.user import UserRole

async def create_admin():
    async with AsyncSessionLocal() as db:
        user_service = UserService(db)
        
        # Check if admin user exists
        admin = await user_service.get_by_email('<EMAIL>')
        if not admin:
            admin_data = {
                'email': '<EMAIL>',
                'username': 'admin',
                'first_name': 'Admin',
                'last_name': 'User',
                'hashed_password': get_password_hash('admin123'),
                'role': UserRole.SUPER_ADMIN,
                'is_active': True,
                'is_verified': True
            }
            await user_service.create(admin_data)
            print('Admin user created: <EMAIL> / admin123')
        else:
            print('Admin user already exists')

asyncio.run(create_admin())
" 2>/dev/null || print_warning "Could not create admin user. You can create one manually after starting the application."
    
    cd ..
}

# Main setup function
main() {
    echo "Global Accounting System Setup"
    echo "=============================="
    echo ""
    echo "Choose setup option:"
    echo "1. Full setup (recommended)"
    echo "2. Backend only"
    echo "3. Frontend only"
    echo "4. Docker setup"
    echo "5. Exit"
    echo ""
    read -p "Enter your choice (1-5): " choice
    
    case $choice in
        1)
            check_requirements
            setup_backend
            setup_frontend
            setup_database
            create_admin_user
            ;;
        2)
            check_requirements
            setup_backend
            setup_database
            create_admin_user
            ;;
        3)
            check_requirements
            setup_frontend
            ;;
        4)
            setup_docker
            ;;
        5)
            print_status "Setup cancelled."
            exit 0
            ;;
        *)
            print_error "Invalid choice. Please run the script again."
            exit 1
            ;;
    esac
    
    echo ""
    print_success "Setup completed successfully! 🎉"
    echo ""
    echo "Next steps:"
    echo "1. Update environment files with your configuration"
    echo "2. Start the backend: cd backend && source venv/bin/activate && uvicorn app.main:app --reload"
    echo "3. Start the frontend: cd frontend && npm run dev"
    echo "4. Visit http://localhost:3000 to access the application"
    echo ""
    echo "Default admin credentials:"
    echo "Email: <EMAIL>"
    echo "Password: admin123"
    echo ""
    print_warning "Please change the default admin password after first login!"
}

# Run main function
main
